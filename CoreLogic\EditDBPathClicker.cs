﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.UI;
using BecaRevitUtilities.Extensions;
using BecaRevitUtilities.RevitCommandsCaller;
using MEP.PowerBIM_5.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class EditDBPathClicker : RevitButtonClicker
    {

        #region Fields



        #endregion

        #region Properties



        #endregion

        #region Constructors

        private EditDBPathClicker(UIApplication uiApp) : base("Dialog_BuildingSystems_RbsEditCircuitGroup:Control_BuildingSystems_RbsEditCircuitPath", uiApp)
        {
           
        }

        public static EditDBPathClicker Create(UIApplication uiApp)
        {
            var clicker = new EditDBPathClicker(uiApp);
            if (clicker._isValid)
            {
                return clicker;
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region Methods
        public bool IsElementValidForSelection(Element e)
        {
            return e.Category.Id.IntegerValue() == (int)BuiltInCategory.OST_ElectricalEquipment;
        }

        protected override bool CanClick()
        {
            var selectedIds = _uiApp.ActiveUIDocument.Selection.GetElementIds();
            if (selectedIds.Count == 1)
            {
                var selElement = _uiApp.ActiveUIDocument.Document.GetElement(selectedIds.First());
                if (IsElementValidForSelection(selElement))
                {
                    return true;
                }
            }
            return false;
        }

        private void ComponentManager_ItemExecuted(object sender, Autodesk.Internal.Windows.RibbonItemExecutedEventArgs e)
        {
            if (e.Item.Id == "ID_RBS_CANCEL_CIRCUIT_PATH_EDIT_MODE")
            {
                //Common.UI.Forms.BecaBaseMessageForm.ShowDialog("Write code", "User canceled edit path command. please write code to handle user cancelation.");
                ModelessPowerBIM_DbEditFormHandler.WakeFormUpDbEdit();
                ModelessPowerBIM_DbEditFormHandler.BringFormsTofront();
                Autodesk.Windows.ComponentManager.ItemExecuted -= ComponentManager_ItemExecuted;
            }
            else if (e.Item.Id == "ID_RBS_FINISH_CIRCUIT_PATH_EDIT_MODE")
            {
                //Common.UI.Forms.BecaBaseMessageForm.ShowDialog("Write code", "User finsished edit path command. please write code to handle user edits.");
                ModelessPowerBIM_DbEditFormHandler.WakeFormUpDbEdit();
                ModelessPowerBIM_DbEditFormHandler.BringFormsTofront();
                Autodesk.Windows.ComponentManager.ItemExecuted -= ComponentManager_ItemExecuted;
                ModelessPowerBIM_DbEditFormHandler.RequestRecalcAndRefreshLengthToForm();

            }
        }

        protected override void PreClickEvent()
        {
            if (_isValid)
            {
                ModelessPowerBIM_DbEditFormHandler.BringFormsToBack();
                Autodesk.Windows.ComponentManager.ItemExecuted += ComponentManager_ItemExecuted;

            }
        }

        #endregion

    }
}
