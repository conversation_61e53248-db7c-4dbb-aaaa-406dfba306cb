
//using Common.Extensions;
using Common.UI.Forms;
using GEN.Easy3DView.CoreLogic;
using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using BecaActivityLogger.CoreLogic.Data;
using Autodesk.Revit.DB;
using BecaRevitElementsCreators;
using Autodesk.Revit.UI;
using MEP.PowerBIM_1._5.UI.ModelessRevitForm;
using BecaRevitUtilities.Collectors;
using System.Text;
using BecaTransactionsNamesManager;
using Color = System.Drawing.Color;
using MEP.PowerBIM_5.UI.Forms;
using Common.Extensions;
using MEP.PowerBIM_5.CoreLogic;
using System.Reflection;
using System.Diagnostics;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class FrmPowerBIM_CircuitEditEnhanced : BecaBaseForm
    {

        #region Fields
        // For Modeless
        RequestHandler _handler;
        ExternalEvent _externalEvent;

        IEnumerable<PowerBIM_CircuitData> _circuitsData;
        PowerBIM_ProjectInfo _powerBIM_ProjectInfo;
        PowerBIM_DBData _DB;
        PowerBIM_DBData _OLD_DB;
        private bool isSavedFlag = false;
        private bool Auto_Calc = true;

        DataGridViewRow LastDR = new DataGridViewRow();
        private int curRowIndex;
        private int LastColIndex;

        List<DataGridViewCell> ChangedCells = new List<DataGridViewCell>();
        string tempChangedCellValue = "";
        string tempLengthValue = "";

        int _pathModeCellNum = 12;
        private bool isReadOnly = false;

        bool _setButtonEnabled;
        private bool _isProcessingManualCheckbox = false;
        #endregion

        #region Constructors

        public FrmPowerBIM_CircuitEditEnhanced(ExternalEvent exEvent, RequestHandler handler, PowerBIM_ProjectInfo pi, PowerBIM_DBData db)
        {
            InitializeComponent();

            // For Modeless
            _handler = handler;
            _externalEvent = exEvent;

            _powerBIM_ProjectInfo = pi;
            _DB = db;

            isReadOnly = db.IsManuallyLocked;

            // Set current DB name
            this.TitleText = "Live Circuit Editor: " + _DB.Schedule_DB_Name;

            _circuitsData = _DB.CCTs.OrderBy(cd => double.Parse(cd.CCT_Number.ExtractNumber()))
                                        .ThenBy(cdata => GetCCTNumberPriority(cdata.CCT_Number));


            //Auto_Calc = true;


            StoreCurrentCircuitsState();

            _setButtonEnabled = true;

            PopulateDiversiviedUndiversifiedLoadTables(db.DB_Element.Document);


            if (_DB.IsManuallyLocked)
            {
                // Set the DataGridView to ReadOnly mode
                AdgvCircuitData.ReadOnly = true;

                // Handle the SelectionChanged and CellEnter events to prevent selection
                AdgvCircuitData.SelectionChanged += (s, e) => AdgvCircuitData.ClearSelection();
                AdgvCircuitData.CellEnter += (s, e) => AdgvCircuitData.ClearSelection();

                // Keep the scrollbars enabled
                AdgvCircuitData.Enabled = true;

                // Disable other controls
                btnRun.Visible = false;
                btnSave.Visible = false;
                btn_ActivateEditPathView.Enabled = false;
                btn_ActivateEditPathView.BackColor = Color.Purple;
                adgv_SearchBar.Enabled = false;
            }

        }

        #endregion

        #region Methods
        #region Modeless Functionality

        /// <summary>
        ///   A private helper method to make a request
        ///   and put the dialog to sleep at the same time.
        /// </summary>
        /// <remarks>
        ///   It is expected that the process which executes the request 
        ///   (the Idling helper in this particular case) will also
        ///   wake the dialog up after finishing the execution.
        /// </remarks>
        ///
        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
            DozeOff();

        }

        /// <summary>
        ///   Control enabler / disabler 
        /// </summary>
        ///
        private void EnableCommands(bool status)
        {
            foreach (System.Windows.Forms.Control ctrl in this.Controls)
            {
                ctrl.Enabled = status;
                this.btnSave.Enabled = true;
                this.btnRun.Enabled = true;
            }
            if (!status)
            {
                this.btnCancel.Enabled = true;

            }
        }

        /// <summary>
        ///   DozeOff -> disable all controls (but the Exit button)
        /// </summary>
        /// 
        private void DozeOff()
        {
            EnableCommands(false);
        }

        /// <summary>
        ///   WakeUp -> enable all controls
        /// </summary>
        /// 
        public void WakeUp()
        {
            EnableCommands(true);
        }

        #endregion

        #region UI

        private void EnhancedFrmPowerBIMcircuitEdit_Load(object sender, EventArgs e)
        {
            AdgvCircuitData.SetDoubleBuffered();

            AdgvCircuitData.onCellValueChanges += AdgvCircuitData_onCellValueChanges;



            int nCount = _circuitsData.Count();
            string progressMessage = "{0} of " + nCount.ToString() + " circuits checked...";
            string caption = "Running PowerBIM circuit check";
            using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, nCount))
            {
                foreach (var circuitData in _circuitsData)
                {
                    circuitData.RunPowerBIMCheck();
                    DataRow dr = enhancedCCTDataSet.Tables[0].NewRow();

                    dr.ItemArray = new object[]
                    {
                    // Inputs
                    circuitData.CCT_Number,
                    circuitData.Breaker.Schedule_Trip_Rating,
                    circuitData.Breaker.Schedule_Curve_Type,
                    circuitData.Breaker.Schedule_Protective_Device,
                    circuitData.Schedule_RCD,
                    circuitData.Schedule_Other_Controls,
                    circuitData.Schedule_Cable_To_First,
                    circuitData.Schedule_Cable_To_Final,
                    circuitData.Schedule_Install_Method,
                    circuitData.Schedule_Derating_Factor*100,
                    circuitData.CCT_Diversity *100,
                    circuitData.LengthClass.Circuit_Path_Mode,
                    Math.Round( circuitData.LengthClass.Length_To_First / 1000 , 1),
                    Math.Round( circuitData.LengthClass.Length_Total / 1000 , 1),
                    Math.Round( circuitData.CCT_PowerBIM_Current,2),

                    circuitData.Number_Of_Elements,
                    circuitData.Schedule_Description,

                    //output
                    circuitData.Schedule_CCTCheck_1_Data,
                    circuitData.Schedule_CCTCheck_2_CableToFirst,
                    circuitData.Schedule_CCTCheck_3_CableToFinal,
                    circuitData.Schedule_CCTCheck_4_Discrimination,
                    circuitData.Schedule_CCTCheck_5_BreakerCurrent,
                    circuitData.Schedule_CCTCheck_6_CableToFirstCurrent,
                    circuitData.Schedule_CCTCheck_7_CableToFinalCurrent,
                    circuitData.Schedule_CCTCheck_8_EFLI,
                    circuitData.Schedule_CCTCheck_9_FinalCircuitVD,
                    circuitData.Schedule_CCTCheck_10_SystemVD,
                    circuitData.Schedule_CCTCheck_11_CableToFirstSC,
                    circuitData.Schedule_CCTCheck_12_CableToFinalSC,
                    circuitData.Schedule_CCTCheck_Summary,
                    circuitData.Schedule_CCTCheck_OK,
                    circuitData.Schedule_Revision,
                    circuitData.CCT_Is_Spare_Or_Space,
                    circuitData.CircuitLengthIsManual

                    };

                    dr.SetField(34, circuitData.ManualCurrent);
                    var manualCurrent = circuitData.Manual_PowerBim_User_Current == 0 ? circuitData.GetCurrent() : circuitData.Manual_PowerBim_User_Current;
                    dr.SetField(35, Math.Round(manualCurrent, 2));


                    enhancedCCTDataSet.Tables[0].Rows.Add(dr);

                    RecalcAndRefreshAllCircuitDataToForm(circuitData, dr);

                    pf.Increment();

                }
            }

            SetAdgvComboboxesDataSources();

            SetPathModeSelectionColumn();

            SetCircuitLengthManualInForm();

            adgv_SearchBar.SetColumns(AdgvCircuitData.Columns);

            MakeRequest(RequestId.WakeFormUpCircuitEditEnhanced);

            isSavedFlag = true;

        }



        #region UI Helpers
        private void SetPathModeSelectionColumn()
        {
            for (int i = 0; i < _circuitsData.Count(); i++)
            {
                UpdatePathModeDatasource(i);
            }
        }

        private void UpdatePathModeDatasource(int rowIndex)
        {
            var dropdownPathMode = AdgvCircuitData.Rows[rowIndex].Cells[_pathModeCellNum] as DataGridViewComboBoxCell;
            dropdownPathMode.DataSource = SetPathModeEnumRows(_circuitsData.ElementAt(rowIndex));

        }

        private List<string> SetPathModeEnumRows(PowerBIM_CircuitData circuitData)
        {
            var ecpList = new List<string>();
            var enums = Enum.GetValues(typeof(Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode)).Cast<Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode>();
            if (!circuitData.HasCustomCircuitPath)
                enums.Where(x => x.ToString() != "Custom").ToList().ForEach(y => ecpList.Add(y.ToString()));
            else
                enums.ToList().ForEach(y => ecpList.Add(y.ToString()));
            return ecpList;
        }

        private void SetCircuitLengthManualInForm()
        {
            for (int i = 0; i < _circuitsData.Count(); i++)
            {
                var checkBoxCell = AdgvCircuitData.Rows[i].Cells[_pathModeCellNum - 1] as DataGridViewCheckBoxCell;
                var paramManual = _circuitsData.ElementAt(i).paramPBLength_Beca_Circuit_Length_Manual;
                if (paramManual == null)
                    return;

                if (!paramManual.HasValue)
                    SetCellsWhenIsNotManual(AdgvCircuitData.Rows[i]);

                if (_circuitsData.ElementAt(i).paramPBLength_Beca_Circuit_Length_Manual.AsInteger() == 1)
                {
                    checkBoxCell.Value = checkBoxCell.TrueValue;
                    SetCellsWhenIsManual(AdgvCircuitData.Rows[i]);
                }
                else if (_circuitsData.ElementAt(i).paramPBLength_Beca_Circuit_Length_Manual.AsInteger() == 0)
                {
                    checkBoxCell.Value = checkBoxCell.FalseValue;
                    SetCellsWhenIsNotManual(AdgvCircuitData.Rows[i]);
                }
            }
        }

        private void SetAdgvComboboxesDataSources()
        {
            deviceRatingDataGridViewTextBoxColumn.DataSource = PowerBIM_Constants.Standard_TripRatings.Keys.ToList();
            deviceCurveTypeDataGridViewTextBoxColumn.DataSource = PowerBIM_Constants.Standard_CurveTypes.Keys.ToList();
            protectionDeviceDataGridViewTextBoxColumn.DataSource = PowerBIM_Constants.Standard_ProtectiveDevices.Keys.ToList();
            rCDProtectionDataGridViewTextBoxColumn.DataSource = PowerBIM_Constants.Standard_RCDProtection.Keys.ToList();

            var standard_cablenames = _powerBIM_ProjectInfo.Standard_CableNames.Values.ToList();
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.DataSource = standard_cablenames;
            var standard_cablenames_without_dash = _powerBIM_ProjectInfo.Standard_CableNames.Values.ToList();
            standard_cablenames_without_dash.Remove("-");
            standard_cablenames_without_dash.Add("REFER TO SCHEDULE");

            cableToFirstCircuitComponentDataGridViewTextBoxColumn.DataSource = standard_cablenames_without_dash;
            cableInstallationMethodDataGridViewTextBoxColumn.DataSource = PowerBIM_Constants.strInstallMethodsList;
        }

        

        private void WriteBackToCCT(PowerBIM_CircuitData CCT, DataRow dtRow)
        {

            CCT.CCT_Number = dtRow.Field<string>("Circuit_Number");

            //Do breaker stuff
            CCT.Breaker.Schedule_Trip_Rating = dtRow.Field<double>("Device_Rating");
            CCT.Breaker.Schedule_Curve_Type = dtRow.Field<string>("Device_Curve_Type");
            CCT.Breaker.Schedule_Protective_Device = dtRow.Field<string>("Protection_Device");
            //CCT.Breaker.UpdateBreakerData();

            // Text stuff
            CCT.Schedule_RCD = dtRow.Field<string>("RCD_Protection");
            CCT.Schedule_Other_Controls = dtRow.Field<string>("Other_Controls");

            // Cable stuff
            CCT.Schedule_Cable_To_First = dtRow.Field<string>("Cable_To_First_Circuit_Component");
            //CCT.Cable_To_First.UpdateCableData();

            CCT.Schedule_Cable_To_Final = dtRow.Field<string>("Cable_To_Remainder_Of_Circuit_Components");
            //CCT.Cable_To_First.UpdateCableData();

            //cable path method
            CCT.LengthClass.Circuit_Path_Mode = dtRow.Field<string>("Path_Mode");

            CCT.Schedule_Install_Method = dtRow.Field<string>("Cable_Installation_Method");

            if (dtRow.ItemArray[9] != DBNull.Value)
            {

                CCT.Schedule_Derating_Factor = dtRow.Field<double>("Derating_Factor") / 100;
            }

            CCT.CCT_Diversity = dtRow.Field<double>("Diversity") / 100; // convert percentage to ratio

            if (dtRow.ItemArray[_pathModeCellNum] != DBNull.Value)
            {
                CCT.LengthClass.Length_To_First = dtRow.Field<double>("Length_To_First") * 1000; // convert to mm
            }
            if (dtRow.ItemArray[12] != DBNull.Value)
            {
                CCT.LengthClass.Length_Total = dtRow.Field<double>("Length_To_Final") * 1000;  // convert to mm
            }

            CCT.ManualCurrent = dtRow.Field<bool>("ManualCurrent");
            CCT.Manual_PowerBim_User_Current = dtRow.Field<double>("ManualCurrentValue");


            CCT.Schedule_Description = dtRow.Field<string>("Circuit_Description");
            CCT.Schedule_Revision = dtRow.Field<string>("Circuit_Revision");

            CCT.Warning_Count = 0; //?


        }

        #endregion

        #region Button Clicks

        private void btnSave_Click(object sender, EventArgs e)
        {
            MakeRequest(RequestId.SaveCircuitEditEnhanced);
        }

        public void SaveCircuitEditEnhanced()
        {
#if TargetYear2025 || TargetYear2026
            var changedRows = Enumerable.DistinctBy(ChangedCells, x => x.Value);
#else
            var changedRows = ChangedCells.AsEnumerable().DistinctBy(x => x.RowIndex);
#endif
            int refreshedCircuitsNum = RecalcAndRefreshAllCircuitsToForm();

            this.AdgvCircuitData.CellFormatting -= new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.AdgvCircuitData_CellFormatting);

            // Commit results back to the revit model
            StringBuilder sb;
            _DB.Commit_CircuitData(out sb);
            if (sb.Length > 0)
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Info", "These circuits cannot be commited:\n\n" + sb.ToString());

            //create new DB object with the dat currently in the actual DB object
            StoreCurrentCircuitsState();

            UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("COMPLETED", "COMPLETE! \n \n" + changedRows.Count() + " Circuits updated in the Revit");

            this.AdgvCircuitData.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.AdgvCircuitData_CellFormatting);

            ChangedCells.Clear();
        }


#endregion

        #region Adgv Behavior

        #region ADGv

        private void adgv_SearchBar_Search(object sender, Zuby.ADGV.AdvancedDataGridViewSearchToolBarSearchEventArgs e)
        {
            var advancedDataGridView_main = AdgvCircuitData;

            bool restartsearch = true;
            int startColumn = 0;
            int startRow = 0;
            if (!e.FromBegin)
            {
                bool endcol = advancedDataGridView_main.CurrentCell.ColumnIndex + 1 >= advancedDataGridView_main.ColumnCount;
                bool endrow = advancedDataGridView_main.CurrentCell.RowIndex + 1 >= advancedDataGridView_main.RowCount;

                if (endcol && endrow)
                {
                    startColumn = advancedDataGridView_main.CurrentCell.ColumnIndex;
                    startRow = advancedDataGridView_main.CurrentCell.RowIndex;
                }
                else
                {
                    startColumn = endcol ? 0 : advancedDataGridView_main.CurrentCell.ColumnIndex + 1;
                    startRow = advancedDataGridView_main.CurrentCell.RowIndex + (endcol ? 1 : 0);
                }
            }
            DataGridViewCell c = advancedDataGridView_main.FindCell(
                e.ValueToSearch,
                e.ColumnToSearch != null ? e.ColumnToSearch.Name : null,
                startRow,
                startColumn,
                e.WholeWord,
                e.CaseSensitive);
            if (c == null && restartsearch)
                c = advancedDataGridView_main.FindCell(
                    e.ValueToSearch,
                    e.ColumnToSearch != null ? e.ColumnToSearch.Name : null,
                    0,
                    0,
                    e.WholeWord,
                    e.CaseSensitive);
            if (c != null)
                advancedDataGridView_main.CurrentCell = c;
        }

        #endregion


        private void AdgvCircuitData_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            DataGridView dgv = sender as DataGridView;

            if ((checkTripRatingDataGridViewTextBoxColumn.Index <= (e.ColumnIndex)) && ((e.ColumnIndex) <= checkCable2SCWithstandDataGridViewTextBoxColumn.Index)) //only highlight error checking columns
            {
                String CellString = dgv.Rows[e.RowIndex].Cells[e.ColumnIndex].Value.ToString();
                if (CellString.Contains("(") || CellString == "") //TODO : pass/fail cell is blank if there is error
                {
                    dgv.Rows[e.RowIndex].Cells[e.ColumnIndex].Style.BackColor = System.Drawing.Color.PaleVioletRed;
                }
                else// if (dgv.Rows[e.RowIndex].Cells[e.ColumnIndex].Style.BackColor == System.Drawing.Color.PaleVioletRed)
                {
                    dgv.Rows[e.RowIndex].Cells[e.ColumnIndex].Style.BackColor = dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor;
                }

                if (isReadOnly)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.LightGray;
                    foreach (DataGridViewCell cell in dgv.Rows[e.RowIndex].Cells)
                    {
                        cell.Style.BackColor = Color.LightGray;
                        if (cell is DataGridViewButtonCell)
                        {
                            var cellBtn = cell as DataGridViewButtonCell;
                            cellBtn.Style.BackColor = Color.LightGray;
                        }
                    }
                    //dgv.Rows[e.RowIndex].BackColor = Color.Black; // TODO: GET BACK HERE! HARRY
                }
            }

            if (!isReadOnly)
            {
                var row = dgv.Rows[e.RowIndex];
                if (bool.TryParse(row.Cells[_pathModeCellNum - 1].Value?.ToString(), out bool result) ? result : false)
                {
                    row.Cells[cableSetPathDataGridViewButtonColumn.Index].ReadOnly = true;
                    row.Cells[cableSetPathDataGridViewButtonColumn.Index].Style.BackColor = System.Drawing.Color.LightGray;
                }
                else
                {
                    row.Cells[cableSetPathDataGridViewButtonColumn.Index].ReadOnly = false;
                    row.Cells[cableSetPathDataGridViewButtonColumn.Index].Style.BackColor = System.Drawing.Color.White;
                }
            }
        }

        private void AdgvCircuitData_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            // Prevent processing during manual checkbox operations to avoid endless loops
            if (_isProcessingManualCheckbox)
            {
                System.Diagnostics.Debug.WriteLine("DataBindingComplete: Skipping processing during manual checkbox operation");
                return;
            }

            System.Diagnostics.Debug.WriteLine("DataBindingComplete: Processing data binding complete event");

            DataGridView dgv = sender as DataGridView;

            foreach (DataGridViewRow row in dgv.Rows)
            {
                if (row.Cells["circuitCheckResultDataGridViewTextBoxColumn"].Value != null)
                {
                    // If it has changed and exists or is na it will have a yellow background.
                    if (row.Cells["circuitCheckResultDataGridViewTextBoxColumn"].Value.ToString() == "OK")
                    {
                        row.Cells["circuitNumberDataGridViewTextBoxColumn"].Style.BackColor = System.Drawing.Color.LightGreen;

                        row.Cells[cablePathSelectionDataGridViewTextBoxColumn.Index].Style.ForeColor = System.Drawing.Color.Green;
                        row.DefaultCellStyle.ForeColor = System.Drawing.Color.DarkGreen;
                    }
                    else
                    {
                        row.Cells[cablePathSelectionDataGridViewTextBoxColumn.Index].Style.ForeColor = System.Drawing.Color.PaleVioletRed;
                        row.Cells["circuitNumberDataGridViewTextBoxColumn"].Style.BackColor = System.Drawing.Color.PaleVioletRed;
                        row.DefaultCellStyle.ForeColor = System.Drawing.Color.DarkRed;
                    }


                }

                if (row.Cells["isSpareOrSpaceDataGridViewCheckBoxColumn"].Value != null)
                {
                    if ((bool)row.Cells["isSpareOrSpaceDataGridViewCheckBoxColumn"].Value == true)
                    {
                        row.Cells["circuitNumberDataGridViewTextBoxColumn"].Style.BackColor = System.Drawing.Color.LightGray;
                        row.DefaultCellStyle.BackColor = System.Drawing.Color.LightGray;
                        row.DefaultCellStyle.ForeColor = System.Drawing.Color.DarkGray;
                        row.ReadOnly = true;
                    }
                }
            }

        }

        bool checkCableFirstvalue = false;
        private void AdgvCircuitData_CurrentCellDirtyStateChanged(object sender, EventArgs e)
        {
            DataGridView dgv = sender as DataGridView;
            DataGridViewCell cell = dgv.CurrentCell;

            // Handle manual checkbox changes here to prevent endless loops
            if (cell != null && cell.ColumnIndex == Manual.Index && cell is DataGridViewCheckBoxCell)
            {
                // Prevent recursive calls when processing manual checkbox
                if (_isProcessingManualCheckbox)
                    return;

                _isProcessingManualCheckbox = true;
                try
                {
                    System.Diagnostics.Debug.WriteLine($"CurrentCellDirtyStateChanged: Processing manual checkbox for row {cell.RowIndex}");

                    // Update current row index
                    curRowIndex = cell.RowIndex;
                    LastColIndex = cell.ColumnIndex;
                    LastDR = dgv.Rows[cell.RowIndex];

                    // Commit the edit to get the new value
                    dgv.CommitEdit(DataGridViewDataErrorContexts.Commit);

                    var row = dgv.Rows[cell.RowIndex];
                    PowerBIM_CircuitData circuit = _circuitsData.ElementAt(cell.RowIndex);

                    if (!Convert.IsDBNull(cell.Value))
                    {
                        // Update UI elements
                        if (Convert.ToBoolean(cell.Value))
                            SetCellsWhenIsManual(row);
                        else
                            SetCellsWhenIsNotManual(row);

                        // Trigger the external event to update Revit parameters
                        ModelessPowerBIM_CircuitEditEnhancedFormHandler.pendingData.Add
                            (
                            new PendingData(RequestId.SetCircuitLengthManual)
                            {
                                CCT = circuit,
                                Cell = cell
                            }
                            );

                        System.Diagnostics.Debug.WriteLine($"CurrentCellDirtyStateChanged: Making external request for row {cell.RowIndex}");
                        MakeRequest(RequestId.SetCircuitLengthManual);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in CurrentCellDirtyStateChanged: {ex.Message}");
                    ModelessPowerBIM_CircuitEditEnhancedFormHandler.ShowMsgToTheUser($"Error updating manual checkbox: {ex.Message}", "Error");
                    // Reset flag on error
                    _isProcessingManualCheckbox = false;
                }
                // Don't reset the flag here - it will be reset after external event processing is complete

                ClearSavedFlag();
                return;
            }

            if (cell is DataGridViewComboBoxCell)
            {
                if (cell.ColumnIndex == otherControlsDataGridViewTextBoxColumn.Index || cell.ColumnIndex == cableToFirstCircuitComponentDataGridViewTextBoxColumn.Index)
                {
                    string columnName = cell.OwningColumn.HeaderText;
                    string value = cell.Value.ToString();
                    if(columnName.Equals("Cable To First") && value == "REFER TO SCHEDULE")
                    {
                        // Get the index of the cell to the right
                        int nextColumnIndex = cell.ColumnIndex + 1;
                        // Check if the next column exists within bounds
                        if (nextColumnIndex < dgv.ColumnCount)
                        {
                            // Access the cell to the right
                            DataGridViewCell nextCell = dgv.Rows[cell.RowIndex].Cells[nextColumnIndex];

                            // Assign the value "-" to the cell to the right
                            nextCell.Value = "-";
                        }

                    }
                    if (value == PowerBIM_CableData.InvalidCableName && checkCableFirstvalue)
                    {
                        ModelessPowerBIM_CircuitEditEnhancedFormHandler.ShowMsgToTheUser("\n\nYou can not choose this value. Changes will be reverted.", "Error");
                        dgv.CancelEdit();
                        dgv.EndEdit();

                    }
                    checkCableFirstvalue = !checkCableFirstvalue;
                }


                dgv.CommitEdit(DataGridViewDataErrorContexts.Commit);
                dgv.EndEdit();
            }

            if (AdgvCircuitData.IsCurrentCellDirty)
            {
                // This fires the cell value changed handler below
                AdgvCircuitData.CommitEdit(DataGridViewDataErrorContexts.Commit);
            }
            ClearSavedFlag();
        }



        private void AdgvCircuitData_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            DataGridView dgv = sender as DataGridView;
            if (e.ColumnIndex == -1 || e.RowIndex == -1)
            {
                return;
            }
            if (ManualCurrent.Index == e.ColumnIndex)
            {
                var row = dgv.Rows[e.RowIndex];

                SetCurrent(row);
            }
            if (Manual.Index == e.ColumnIndex)
            {
                // For manual checkbox, don't trigger external events here
                // The processing will be handled by the CellEndEdit event instead
                // This prevents the endless loop issue
                return;
            }
        }

        public void SetCircuitLengthManual(PowerBIM_CircuitData CCT, DataGridViewCell cell)
        {
            System.Diagnostics.Debug.WriteLine($"SetCircuitLengthManual: Starting processing for row {cell.RowIndex}, flag is {_isProcessingManualCheckbox}");

            try
            {
                if (Convert.ToBoolean(cell.Value))
                {
                    CCT.paramPBLength_Beca_Circuit_Length_Manual?.Set(1);
                    CCT.CircuitLengthIsManual = true;
                }
                else
                {
                    CCT.paramPBLength_Beca_Circuit_Length_Manual?.Set(0);
                    CCT.CircuitLengthIsManual = false;
                    if (Auto_Calc)
                    {
                        if (cell.RowIndex == -1)
                            return;

                        // Don't trigger recalc during manual checkbox processing to prevent loops
                        // The recalc will be handled by the external event system
                        System.Diagnostics.Debug.WriteLine($"SetCircuitLengthManual: Skipping RecalcAndRefreshAllCircuitDataToForm for row {cell.RowIndex} to prevent loops");
                        // RecalcAndRefreshAllCircuitDataToForm(CCT, enhancedCCTDataSet.Tables[0].Rows[cell.RowIndex]);
                    }
                    // Else, if we arent allowed to recalc, flag this on the gUI by making yellow :D
                    else
                    {
                        // We want to set the row to yellow, but keep the circuit number column showing green or red as it was previosuly.
                        var tempColorStore = LastDR.Cells["circuitNumberDataGridViewTextBoxColumn"].Style.BackColor;
                        LastDR.DefaultCellStyle.BackColor = System.Drawing.Color.LightYellow;
                        LastDR.Cells["circuitNumberDataGridViewTextBoxColumn"].Style.BackColor = tempColorStore;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"SetCircuitLengthManual: Completed processing for row {cell.RowIndex}, resetting flag");
            }
            finally
            {
                // Reset the flag after external event processing is complete
                _isProcessingManualCheckbox = false;
                System.Diagnostics.Debug.WriteLine($"SetCircuitLengthManual: Flag reset to {_isProcessingManualCheckbox}");
            }
        }

        public void WriteLengthAfterUserInputToCircuitParameter(PowerBIM_CircuitData CCT, DataGridViewCell cell)
        {
            // If first length changed
            if (cell.ColumnIndex == _pathModeCellNum + 2)
                CCT.CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidLen_1stElem).SetValueString((Convert.ToDouble(cell.Value.ToString()) * 1000).ToString());
            // If total length changed
            if (cell.ColumnIndex == _pathModeCellNum + 3)
                CCT.CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidLen_Total).SetValueString((Convert.ToDouble(cell.Value.ToString()) * 1000).ToString());
        }

        private void AdgvCircuitData_SelectionChanged(object sender, EventArgs e)
        {
            var senderGrid = (DataGridView)sender;
            DataGridViewCell cell = senderGrid.CurrentCell;

            // Update current row index if we have a valid current cell
            if (cell != null && cell.RowIndex >= 0)
            {
                curRowIndex = cell.RowIndex;
            }

            // Prevent processing during manual checkbox operations
            if (_isProcessingManualCheckbox)
                return;

            if (LastDR != null && !LastDR.Cells[LastColIndex].ReadOnly)
            {
                if (LastDR.Cells["isSpareOrSpaceDataGridViewCheckBoxColumn"].Value != null)
                {
                    if ((bool)LastDR.Cells["isSpareOrSpaceDataGridViewCheckBoxColumn"].Value == false)
                    {
                        // check if these are dropdown menues
                        if (senderGrid.Columns[LastColIndex] is DataGridViewTextBoxColumn && curRowIndex >= 0)
                        {
                            var CCT = _circuitsData.ElementAt(curRowIndex);
                            var row = enhancedCCTDataSet.Tables[0].Rows[curRowIndex];

                            //
                            // Perform powerbim check on new circuit - wo only do this if auto is on
                            //

                            if (Auto_Calc)
                            {

                                ModelessPowerBIM_CircuitEditEnhancedFormHandler.pendingData.Add
                                                                                (
                                                                                new PendingData(RequestId.RecalcAndRefreshCircuitToForm)
                                                                                {
                                                                                    CCT = CCT,
                                                                                    Row = row
                                                                                }
                                                                                );

                                MakeRequest(RequestId.RecalcAndRefreshCircuitToForm);

                                //RecalcAndRefreshCircuitToForm(CCT, row);
                            }
                            // Else, if we arent allowed to recalc, flag this on the gUI by making yellow :D
                            else
                            {
                                // We want to set the row to yellow, but keep the circuit number column showing green or red as it was previosuly. 
                                var tempColorStore = LastDR.Cells["circuitNumberDataGridViewTextBoxColumn"].Style.BackColor;
                                LastDR.DefaultCellStyle.BackColor = System.Drawing.Color.LightYellow;
                                LastDR.Cells["circuitNumberDataGridViewTextBoxColumn"].Style.BackColor = tempColorStore;
                            }
                            //AdgvCircuitData.Refresh();
                        }
                    }
                }
            }
            ClearSavedFlag();
        }


        private void AdgvCircuitData_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex == -1 || isReadOnly)
            {
                return;
            }

            // Update current row index immediately when clicking on any cell
            curRowIndex = e.RowIndex;
            LastColIndex = e.ColumnIndex;

            var senderGrid = (DataGridView)sender;
            DataGridViewCell cell = senderGrid[e.ColumnIndex, e.RowIndex];
            LastDR = senderGrid.Rows[e.RowIndex];


            if (cell.ColumnIndex == numberOfElementsDataGridViewButtonColumn.Index)
            {
                PowerBIM_CircuitData circuit = _circuitsData.ElementAt(e.RowIndex);

                using (frmElementsOnCircuit frm = new frmElementsOnCircuit(circuit))
                {
                    frm.ShowDialog(this);
                }
                return;
            }

            if (senderGrid.Columns[e.ColumnIndex] is DataGridViewButtonColumn && e.RowIndex >= 0)
            {
                if (AdgvCircuitData.Rows[cell.RowIndex].Cells[_pathModeCellNum - 1].Value.ToString() == "True")
                    return;

                //open 3d view to move path
                PowerBIM_CircuitData circuit = _circuitsData.ElementAt(e.RowIndex);
                //circuit.LengthClass.OpenPathCustomisingView(circuit);

                // Modeless
                ModelessPowerBIM_CircuitEditEnhancedFormHandler.pendingData.Add
                                                                (
                                                                new PendingData(RequestId.OpenPathCustomisingView)
                                                                {
                                                                    CCT = circuit,
                                                                    Row = enhancedCCTDataSet.Tables[0].Rows[e.RowIndex]
                                                                }
                                                                );

                MakeRequest(RequestId.OpenPathCustomisingView);

                //RecalcAndRefreshLengthToForm(circuit, enhancedCCTDataSet.Tables[0].Rows[e.RowIndex]);
            }

            // Manual checkbox processing is now handled in CurrentCellDirtyStateChanged
            // to prevent endless loops. This section is kept for other checkbox columns if any.
            if (senderGrid.Columns[e.ColumnIndex] is DataGridViewCheckBoxColumn && e.RowIndex >= 0)
            {
                // Skip manual checkbox processing here - it's handled in CurrentCellDirtyStateChanged
                if (e.ColumnIndex == Manual.Index)
                    return;

                // Handle other checkbox columns here if needed
            }
        }

        private void SetCellsWhenIsManual(DataGridViewRow row)
        {
            // Disable and greyed out path mode 
            row.Cells[_pathModeCellNum].ReadOnly = true;
            row.Cells[_pathModeCellNum].Style.ForeColor = System.Drawing.Color.White;
            row.Cells[_pathModeCellNum].Style.BackColor = System.Drawing.Color.LightGray;
            // Disable and greyed out set button 
            row.Cells[_pathModeCellNum + 1].ReadOnly = true;
            // Enable user input first length
            row.Cells[_pathModeCellNum + 2].ReadOnly = false;
            row.Cells[_pathModeCellNum + 2].Style.BackColor = System.Drawing.Color.White;
            // Enable user input total length
            row.Cells[_pathModeCellNum + 3].ReadOnly = false;
            row.Cells[_pathModeCellNum + 3].Style.BackColor = System.Drawing.Color.White;




        }


        private void SetRowsLocked(DataGridViewRow row)
        {

        }

        private void SetCellsWhenIsNotManual(DataGridViewRow row)
        {
            // Enable path mode
            row.Cells[_pathModeCellNum].ReadOnly = false;
            row.Cells[_pathModeCellNum].Style.ForeColor = System.Drawing.Color.Green;
            row.Cells[_pathModeCellNum].Style.BackColor = System.Drawing.Color.White;
            // Enable path mode
            row.Cells[_pathModeCellNum + 1].ReadOnly = false;
            // Disable user and greyed out  input first length
            row.Cells[_pathModeCellNum + 2].ReadOnly = true;
            row.Cells[_pathModeCellNum + 2].Style.BackColor = System.Drawing.Color.LightGray;
            // Disable user input and greyed out  total length
            row.Cells[_pathModeCellNum + 3].ReadOnly = true;
            row.Cells[_pathModeCellNum + 3].Style.BackColor = System.Drawing.Color.LightGray;


        }

        private void AdgvCircuitData_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            if (e.Exception.Message == "DataGridViewComboBoxCell value is not valid.")
            {
                var dgvFrmCircuitData = sender as DataGridView;
                object value = dgvFrmCircuitData[e.ColumnIndex, e.RowIndex].Value;
                if (!dgvFrmCircuitData.Columns[e.ColumnIndex].Name.Contains(value.ToString()))
                {
                    dgvFrmCircuitData.Columns[e.ColumnIndex].Name = value.ToString();
                    e.ThrowException = false;
                }
            }
        }


        #endregion

#endregion

        private int GetCCTNumberPriority(string CCT_Number)
        {
            var checkVal = CCT_Number.ToLower();
            if (checkVal.Contains("r"))
            {
                return 0;
            }
            else if (checkVal.Contains("w"))
            {
                return 1;
            }
            else if (checkVal.Contains("b"))
            {
                return 2;
            }
            else
            {
                return 100;
            }
        }


#endregion

        private void PopulateDiversiviedUndiversifiedLoadTables(Document doc)
        {
            dgv_DiversifiedPhase.Rows.Add(Math.Round(_DB.Diversified_Total_Phase_Current_R, 2), Math.Round(_DB.Diversified_Total_Phase_Current_W, 2), Math.Round(_DB.Diversified_Total_Phase_Current_B, 2));
            dgv_UnDiversifiedPhase.Rows.Add(Math.Round(_DB.UnDiversified_Total_Phase_Current_R,2), Math.Round(_DB.UnDiversified_Total_Phase_Current_W,2), Math.Round(_DB.UnDiversified_Total_Phase_Current_B,2));
        }

        private void btnRun_Click(object sender, EventArgs e)
        {
            MakeRequest(RequestId.AutoCalc);

        }

        public void AutoCalc()
        {
            if (btnRun.Text == "Auto Calc [ON]")
            {
                btnRun.Text = "Auto Calc [OFF]";
                btnRun.BackColor = System.Drawing.Color.Black;
                Auto_Calc = false;

            }
            else
            {
                btnRun.Text = "Auto Calc [ON]";
                btnRun.BackColor = System.Drawing.Color.FromArgb(141, 14, 132);
                btnRun.ForeColor = System.Drawing.Color.White;
                Auto_Calc = true;

                //we need to refresh all incase some have changed
                RecalcAndRefreshAllCircuitsToForm();
            }
        }

        public void RevertToOldSave()
        {
            int i = 0;

            // go through the circuits to revert to the state it was in when the oldDB object was made
            foreach (PowerBIM_CircuitData CCT in _OLD_DB.CCTs)
            {
                _DB.CCTs[i] = null;
                _DB.CCTs[i] = CCT;

                _DB.CCTs[i].Refresh_DerrivedCircuitProperties();

                _DB.CCTs[i].SetCircuitLengthToRevit();

                i++;
            }
        }

        private void SetSavedFlag()
        {
            //change colour of button and disable it
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.btnSave.ForeColor = System.Drawing.Color.White;
            this.btnSave.Enabled = false;

            isSavedFlag = true;//set flag
        }

        private void ClearSavedFlag()
        {
            //change colour of button and enable it
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnSave.ForeColor = System.Drawing.Color.Black;
            this.btnSave.Enabled = true;

            isSavedFlag = false;//clear flag
        }

        private void StoreCurrentCircuitsState()
        {
            //store current circuit parameters into a new variable where they wont be changed
            _OLD_DB = null;
            _OLD_DB = new PowerBIM_DBData(_powerBIM_ProjectInfo, _DB.DB_Element);
            _OLD_DB.Initialise_AllCircuits();

            ModelessPowerBIM_CircuitEditEnhancedFormHandler._OLD_DB = _OLD_DB;
            //MakeRequest(RequestId.Initialise_AllCircuits);

            SetSavedFlag();
        }

        private void FrmClosing(object sender, FormClosingEventArgs e)
        {
            //called when exiting the form
            //if the active DB and circuit objects are the same as when saved do nothing 
            //but if there are unsaved changes warn user

            if (!isSavedFlag)
            {
                DialogResult result = MessageBox.Show("You have unsaved changes. Would you like to save those changes?", "Warning", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                {
                    btnSave_Click(sender, e);//save circuit 
                }
                else if (result == DialogResult.No)
                {
                    MakeRequest(RequestId.RevertToOldSave);
                    ModelessPowerBIM_StartFormHandler.BringFormsTofront();

                    return;
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                    return;
                }

            }

            MakeRequest(RequestId.WakeFormUpStartForm);
            ModelessPowerBIM_StartFormHandler.BringFormsTofront();

            return;
        }

        public void RecalcAndRefreshLengthToForm()
        {
            MakeRequest(RequestId.RecalcAndRefreshLengthToFormClick);

        }

        public void RecalcAndRefreshLengthToFormClick(PowerBIM_CircuitData CCT, DataRow row)
        {
            UpdatePathModeDatasource(AdgvCircuitData.CurrentRow.Index);
            //Get latest path mode selection from revit and update GUI
            CCT.readCircuitPathMode();
            row.SetField<string>("Path_Mode", CCT.LengthClass.Circuit_Path_Mode);

            //recalculate all the numbers
            // Flag CCT that path mode has changed from the form
            CCT.PathmodeChangedInEditCircuit = true;
            CCT.Refresh_DerrivedCircuitProperties();

            //run checks
            CCT.RunPowerBIMCheck();

            //finally put values from circuit into table
            row.SetField<string>("Length_To_First", Math.Round(CCT.LengthClass.Length_To_First / 1000, 1).ToString());
            row.SetField<string>("Length_To_Final", Math.Round(CCT.LengthClass.Length_Total / 1000, 1).ToString());
            row.SetField<string>("Cable_To_First_Circuit_Component", CCT.Schedule_Cable_To_First);
            row.SetField<string>("Cable_To_Remainder_Of_Circuit_Components", CCT.Schedule_Cable_To_Final);
            row.SetField<string>("Check_Trip_Rating", CCT.Schedule_CCTCheck_1_Data);
            row.SetField<string>("Cable_1_Valid", CCT.Schedule_CCTCheck_2_CableToFirst);
            row.SetField<string>("Cable_2_Valid", CCT.Schedule_CCTCheck_3_CableToFinal);
            row.SetField<string>("Check_CPD_Descriminates", CCT.Schedule_CCTCheck_4_Discrimination);
            row.SetField<string>("Check_Load_Current", CCT.Schedule_CCTCheck_5_BreakerCurrent);
            row.SetField<string>("Check_Cable_1_Current", CCT.Schedule_CCTCheck_6_CableToFirstCurrent);
            row.SetField<string>("Check_Cable_2_Current", CCT.Schedule_CCTCheck_7_CableToFinalCurrent);
            row.SetField<string>("Check_EFLI", CCT.Schedule_CCTCheck_8_EFLI);
            row.SetField<string>("Check_Final_CCT_VD", CCT.Schedule_CCTCheck_9_FinalCircuitVD);
            row.SetField<string>("Check_System_Max_VD", CCT.Schedule_CCTCheck_10_SystemVD);
            row.SetField<string>("Check_Cable_1_SC_Withstand", CCT.Schedule_CCTCheck_11_CableToFirstSC);
            row.SetField<string>("Check_Cable_2_SC_Withstand", CCT.Schedule_CCTCheck_12_CableToFinalSC);
            row.SetField<string>("Circuit_Check_Summary", CCT.Schedule_CCTCheck_Summary);
            row.SetField<string>("Circuit_Check_Result", CCT.Schedule_CCTCheck_OK);
        }

        public void RecalcAndRefreshAllCircuitDataToForm(PowerBIM_CircuitData CCT, DataRow row)
        {
            string firstCableName = row[6]?.ToString();

            

            if (firstCableName.Equals("REFER TO SCHEDULE"))
            {
                // Filter rows where column 6 equals "REFER TO SCHEDULE"
                List<DataGridViewRow> rowsToProcess = AdgvCircuitData.Rows.Cast<DataGridViewRow>()
                    .Where(row => row.Cells[6]?.Value?.ToString() == "REFER TO SCHEDULE")
                    .ToList();

                CCT.SetCablesToReferToSchedule();
                //dgvRow TODO: SET THIS! 


                foreach(DataGridViewRow dgvRow in rowsToProcess)
                {
                    dgvRow.Cells[24].Value = "";
                    dgvRow.Cells[25].Value = "";
                    dgvRow.Cells[26].Value = "";
                    dgvRow.Cells[27].Value = "";
                    dgvRow.Cells[28].Value = "";
                    dgvRow.Cells[29].Value = "";
                    dgvRow.Cells[30].Value = "";
                    dgvRow.Cells[31].Value = "";
                    dgvRow.Cells[32].Value = "SKIPPED - PowerBIM will not validate this Circuit.";
                    DataRow dataRow = (dgvRow.DataBoundItem as DataRowView)?.Row;
                    WriteBackToCCT(CCT, dataRow);

                }

                return;

            }
            else
            {
                //get the most recent circuit values into the 
                WriteBackToCCT(CCT, row);
            }
                

                

            //recalculate all the numbers
            CCT.Refresh_DerrivedCircuitProperties();

            //run checks
            CCT.RunPowerBIMCheck();

            CCT.CheckDefinedCableWarning(CCT.Schedule_Cable_To_First);
            CCT.CheckDashCableWarning(CCT);
            CCT.CheckEmergencyLightingExistsWarning();
            CCT.CheckDeratingFactorIsAboveOneWarning(CCT.Schedule_Derating_Factor);
            CCT.CheckRCDElementIsPresentWarning(CCT.CCT_RCD_ElementIsPresent);

            // Add warning messages (non critical)
            if (CCT.Warning_Count > 0)
            {
                CCT.AddWarningMessages();
            }

            //finally put values from circuit into table
            if (!CCT.CircuitLengthIsManual)
            {
                row.SetField<string>("Length_To_First", Math.Round(CCT.LengthClass.Length_To_First / 1000, 1).ToString());
                row.SetField<string>("Length_To_Final", Math.Round(CCT.LengthClass.Length_Total / 1000, 1).ToString());
            }
            row.SetField<string>("Cable_To_First_Circuit_Component", CCT.Schedule_Cable_To_First);
            row.SetField<string>("Cable_To_Remainder_Of_Circuit_Components", CCT.Schedule_Cable_To_Final);
            row.SetField<string>("Check_Trip_Rating", CCT.Schedule_CCTCheck_1_Data);
            row.SetField<string>("Cable_1_Valid", CCT.Schedule_CCTCheck_2_CableToFirst);
            row.SetField<string>("Cable_2_Valid", CCT.Schedule_CCTCheck_3_CableToFinal);
            row.SetField<string>("Check_CPD_Descriminates", CCT.Schedule_CCTCheck_4_Discrimination);
            row.SetField<string>("Check_Load_Current", CCT.Schedule_CCTCheck_5_BreakerCurrent);
            row.SetField<string>("Check_Cable_1_Current", CCT.Schedule_CCTCheck_6_CableToFirstCurrent);
            row.SetField<string>("Check_Cable_2_Current", CCT.Schedule_CCTCheck_7_CableToFinalCurrent);
            row.SetField<string>("Check_EFLI", CCT.Schedule_CCTCheck_8_EFLI);
            row.SetField<string>("Check_Final_CCT_VD", CCT.Schedule_CCTCheck_9_FinalCircuitVD);
            row.SetField<string>("Check_System_Max_VD", CCT.Schedule_CCTCheck_10_SystemVD);
            row.SetField<string>("Check_Cable_1_SC_Withstand", CCT.Schedule_CCTCheck_11_CableToFirstSC);
            row.SetField<string>("Check_Cable_2_SC_Withstand", CCT.Schedule_CCTCheck_12_CableToFinalSC);
            row.SetField<string>("Circuit_Check_Summary", CCT.Schedule_CCTCheck_Summary);
            row.SetField<string>("Circuit_Check_Result", CCT.Schedule_CCTCheck_OK);

            // Temporary update DB
            var clonedDB = CloneDBTemporary(CCT.DB);
            clonedDB.CalculateDiversifiedCircuitLoad();
            dgv_DiversifiedPhase.Rows.Clear();
            dgv_DiversifiedPhase.Rows.Add(Math.Round(clonedDB.Diversified_Total_Phase_Current_R, 2), Math.Round(clonedDB.Diversified_Total_Phase_Current_W, 2), Math.Round(clonedDB.Diversified_Total_Phase_Current_B, 2));
            dgv_UnDiversifiedPhase.Rows.Clear();
            dgv_UnDiversifiedPhase.Rows.Add(Math.Round(clonedDB.UnDiversified_Total_Phase_Current_R, 2), Math.Round(clonedDB.UnDiversified_Total_Phase_Current_W, 2), Math.Round(clonedDB.UnDiversified_Total_Phase_Current_B, 2));

            



        }

        internal PowerBIM_DBData CloneDBTemporary(PowerBIM_DBData DB)
        {
            // Create a new instance of the DB object using reflection
            PowerBIM_DBData DB_Cloned = (PowerBIM_DBData)Activator.CreateInstance(DB.GetType(), _powerBIM_ProjectInfo, DB.DB_Element);

            // Get all the properties of the DB object and copy them to DB_Copy
            foreach (PropertyInfo property in DB.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                if (property.CanWrite)  // Only copy properties that can be written to
                {
                    property.SetValue(DB_Cloned, property.GetValue(DB, null), null);
                }
            }

            // Copy fields 
            foreach (FieldInfo field in DB.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance))
            {
                field.SetValue(DB_Cloned, field.GetValue(DB));
            }

            DB_Cloned.Diversified_Total_Phase_Current_R = DB.Diversified_Total_Phase_Current_R;
            DB_Cloned.Diversified_Total_Phase_Current_B = DB.Diversified_Total_Phase_Current_B;
            DB_Cloned.Diversified_Total_Phase_Current_W = DB.Diversified_Total_Phase_Current_W;

            return DB_Cloned;
        }

        private int RecalcAndRefreshAllCircuitsToForm()
        {
            //this goes through all circuits and put the data from the sheet back into the CCT params
            //then it recalculates the derived properties then it updates the data in the data grid view

            //it returns how many circuits where refreshed

            int i = 0;

            var dgvCCTData_1 = enhancedCCTDataSet.Tables[0].Rows;
            // Populate new DB data class for Datagrid viewer
            int nCount = _circuitsData.Count();
            string progressMessage = "{0} of " + nCount.ToString() + " circuits processed...";
            string caption = "Calculating circuit data";

            using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, nCount))
            {
                foreach (PowerBIM_CircuitData CCT in _circuitsData)
                {
                    // Push back the updated CCT data into the CCT class
                    RecalcAndRefreshAllCircuitDataToForm(CCT, dgvCCTData_1[i]);

                    i++;

                    pf.Increment();
                }
                return i;
            }

        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void AdgvCircuitData_CellBeginEdit(object sender, DataGridViewCellCancelEventArgs e)
        {
            tempChangedCellValue = AdgvCircuitData[e.ColumnIndex, e.RowIndex].Value.ToString();

            // Store Length value
            if (e.ColumnIndex == _pathModeCellNum + 2 || e.ColumnIndex == _pathModeCellNum + 2)
            {
                tempLengthValue = AdgvCircuitData[e.ColumnIndex, e.RowIndex].Value.ToString();
            }


        }

        private void AdgvCircuitData_onCellValueChanges(DataGridViewCell cell, object OldValue)
        {
            // Prevent recursive calls when processing manual checkbox
            if (_isProcessingManualCheckbox && cell.ColumnIndex == Manual.Index)
                return;

            tempChangedCellValue = OldValue.ToString();
            AdgvCircuitData_CellEndEdit(AdgvCircuitData, new DataGridViewCellEventArgs(cell.ColumnIndex, cell.RowIndex));

            // Only call CellContentClick for non-manual checkbox cells to avoid infinite loops
            if (cell.ColumnIndex != Manual.Index)
            {
                AdgvCircuitData_CellContentClick(AdgvCircuitData, new DataGridViewCellEventArgs(cell.ColumnIndex, cell.RowIndex));
            }
        }

        private void AdgvCircuitData_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == -1 || e.RowIndex == -1)
            {
                return;
            }

            if (AdgvCircuitData[e.ColumnIndex, e.RowIndex] != null && !string.IsNullOrEmpty(tempChangedCellValue))
            {
                if (tempChangedCellValue != AdgvCircuitData[e.ColumnIndex, e.RowIndex].Value?.ToString())
                {
                    ChangedCells.Add(AdgvCircuitData[e.ColumnIndex, e.RowIndex]);
                }
            }

            if (AdgvCircuitData[e.ColumnIndex, e.RowIndex] != null && !AdgvCircuitData[e.ColumnIndex, e.RowIndex].ReadOnly)
            {
                var senderGrid = (DataGridView)sender;
                DataGridViewCell cell = senderGrid[e.ColumnIndex, e.RowIndex];
                LastDR = AdgvCircuitData.Rows[e.RowIndex];
                curRowIndex = e.RowIndex;
                LastColIndex = e.ColumnIndex;


                if (LastDR.Cells["isSpareOrSpaceDataGridViewCheckBoxColumn"].Value != null)
                {
                    if ((bool)LastDR.Cells["isSpareOrSpaceDataGridViewCheckBoxColumn"].Value == false)
                    {
                        var CCT = _circuitsData.ElementAt(curRowIndex);
                        MEP.PowerBIM_5.DataSets.EnhancedCCTDataSet.EnhancedCCTTableRow
                        row = enhancedCCTDataSet.Tables[0].Rows[curRowIndex] as MEP.PowerBIM_5.DataSets.EnhancedCCTDataSet.EnhancedCCTTableRow;


                        var currnetValueChanged = e.ColumnIndex == unDiversifiedCurrentDataGridViewTextBoxColumn.Index && cell.Value != null;
                        // Check if derating factor is above 1
                        var deratingfactorCheck = e.ColumnIndex == deratingFactorDataGridViewTextBoxColumn.Index && !string.IsNullOrEmpty(AdgvCircuitData[e.ColumnIndex, e.RowIndex].Value.ToString());
                        var isPathModeChanged = e.ColumnIndex == cablePathSelectionDataGridViewTextBoxColumn.Index && cell.Value != null;
                        var cable_to_remainder_changed = false;


                        var dataPropertyName = AdgvCircuitData.Columns[e.ColumnIndex].DataPropertyName;
                        if (!string.IsNullOrEmpty(dataPropertyName))
                        {
                            row[dataPropertyName] = cell.Value;
                            cable_to_remainder_changed = dataPropertyName.Contains("Cable_To_Remainder");
                        }
                        if (currnetValueChanged)
                        {
                            row.ManualCurrentValue = double.Parse(cell.Value.ToString());
                        }


                        if (cable_to_remainder_changed)
                        {
                            CCT.Cable_To_Final.Cable_Name = cell.Value.ToString();
                        }


                        if (deratingfactorCheck)
                        {
                            _circuitsData.ElementAt(curRowIndex).CheckDeratingFactorIsAboveOneWarning(Convert.ToDouble(AdgvCircuitData[e.ColumnIndex, e.RowIndex].Value.ToString()) / 100);
                        }

                        if (e.ColumnIndex == _pathModeCellNum)
                        {
                            // Flag CCT that path mode has changed from the form
                            CCT.PathmodeChangedInEditCircuit = true;
                        }

                        //
                        // Perform powerbim check on new circuit - wo only do this if auto is on
                        //


                        if (Auto_Calc)
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.pendingData.Add(new PendingData(RequestId.RecalcAndRefreshCircuitToForm) { CCT = CCT, Row = row });
                            MakeRequest(RequestId.RecalcAndRefreshCircuitToForm);

                            //RecalcAndRefreshCircuitToForm(CCT, row);
                        }
                        // Else, if we arent allowed to recalc, flag this on the gUI by making yellow :D
                        else
                        {
                            // We want to set the row to yellow, but keep the circuit number column showing green or red as it was previosuly. 
                            var tempColorStore = LastDR.Cells["circuitNumberDataGridViewTextBoxColumn"].Style.BackColor;
                            LastDR.DefaultCellStyle.BackColor = System.Drawing.Color.LightYellow;
                            LastDR.Cells["circuitNumberDataGridViewTextBoxColumn"].Style.BackColor = tempColorStore;
                        }


                        // If Manual checkbox changes - skip external event here
                        // The processing is now handled in CellContentClick to prevent loops
                        if (e.ColumnIndex == Manual.Index && e.RowIndex >= 0)
                        {
                            // Don't trigger external events here to prevent endless loop
                            return;
                        }

                        // Write to parameter when there's user input when manual length has changed
                        if ((senderGrid.Columns[e.ColumnIndex].Index == _pathModeCellNum + 2 || senderGrid.Columns[e.ColumnIndex].Index == _pathModeCellNum + 3) && e.RowIndex >= 0)
                        {
                            ModelessPowerBIM_CircuitEditEnhancedFormHandler.pendingData.Add
                                                                           (
                                                                           new PendingData(RequestId.WriteLengthAfterUserInputToCircuitParameter)
                                                                           {
                                                                               CCT = CCT,
                                                                               Cell = cell
                                                                           }
                                                                           );
                            MakeRequest(RequestId.WriteLengthAfterUserInputToCircuitParameter);
                        }
                    }
                }
            }

            ClearSavedFlag();

            // Don't refresh during manual checkbox processing to prevent endless loops
            if (!_isProcessingManualCheckbox)
            {
                AdgvCircuitData.Refresh();
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("CellEndEdit: Skipping Refresh() during manual checkbox processing");
            }
        }

        private void AdgvCircuitData_CellLeave(object sender, DataGridViewCellEventArgs e)
        {
            var senderGrid = (DataGridView)sender;
            DataGridViewCell cell = senderGrid[e.ColumnIndex, e.RowIndex];
            curRowIndex = e.RowIndex;
            var CCT = _circuitsData.ElementAt(curRowIndex);
            var row = enhancedCCTDataSet.Tables[0].Rows[curRowIndex];
            var cellValue = AdgvCircuitData[e.ColumnIndex, e.RowIndex]?.Value;

            if(cellValue is null)
                return;

            // Set length parameter if user changed length value
            if (!AdgvCircuitData[e.ColumnIndex, e.RowIndex].ReadOnly && tempLengthValue != cellValue.ToString())
            {
                if (AdgvCircuitData[e.ColumnIndex, e.RowIndex].ColumnIndex == _pathModeCellNum + 2)
                {
                    ModelessPowerBIM_CircuitEditEnhancedFormHandler.pendingData.Add
                                               (
                                               new PendingData(RequestId.SetFirstLengthManual)
                                               {
                                                   CCT = CCT,
                                                   Cell = cell
                                               }
                                               );

                    MakeRequest(RequestId.SetFirstLengthManual);
                }
                else if (AdgvCircuitData[e.ColumnIndex, e.RowIndex].ColumnIndex == _pathModeCellNum + 3)
                {
                    ModelessPowerBIM_CircuitEditEnhancedFormHandler.pendingData.Add
                           (
                           new PendingData(RequestId.SetTotalLengthManual)
                           {
                               CCT = CCT,
                               Cell = cell
                           }
                           );

                    MakeRequest(RequestId.SetTotalLengthManual);
                }

            }
        }

        public void SetFirstLengthManual(PowerBIM_CircuitData CCT, DataGridViewCell cell)
        {
            CCT.CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidCbl_1stElem).SetValueString(cell.Value.ToString());
        }

        public void SetTotalLengthManual(PowerBIM_CircuitData CCT, DataGridViewCell cell)
        {
            CCT.CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidLen_Total).SetValueString(cell.Value.ToString());
        }

        private void btn_ActivateEditPathView_Click(object sender, EventArgs e)
        {
            if (!isReadOnly)
            {
                MakeRequest(RequestId.ActivateEdithPathView);
            }
        }

        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            this.TopMost = false;
            if (Environment.UserDomainName == "BECAMAIL")
            {
                using (frmPowerBIM_Help rqFrmHelp = new frmPowerBIM_Help())
                {
                    rqFrmHelp.ShowDialog();
                }
            }
            else
            {
                using (var bedarHelpFrm = new frmBedarPowerBIM_Help())
                {
                    bedarHelpFrm.ShowDialog();
                }
            }
        }

        private void AdgvCircuitData_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
        {
            DataGridView dgv = sender as DataGridView;
            if (e.RowIndex == -1)
            {
                return;
            }

            SetCurrent(dgv.Rows[e.RowIndex]);
        }

        private void SetCurrent(DataGridViewRow row)
        {
            bool isManual = (bool)row.Cells[ManualCurrent.Index].Value;
            row.Cells[ManualCurrent.Index + 1].ReadOnly = !isManual;
            row.Cells[ManualCurrent.Index + 1].Style.BackColor = isManual ? Color.White : Color.LightGray;
            var dtv = row.DataBoundItem as DataRowView;

            var dt = dtv.Row as MEP.PowerBIM_5.DataSets.EnhancedCCTDataSet.EnhancedCCTTableRow;

            if (isManual)
            {
                row.Cells[ManualCurrent.Index + 1].Value = dt.ManualCurrentValue;
            }
            else
            {
                row.Cells[ManualCurrent.Index + 1].Value = dt.PowerBimCurrent;
            }
        }


    }

    #region Modeless Form Handler 

    public static class ModelessPowerBIM_CircuitEditEnhancedFormHandler
    {
        #region Fields
        public static FrmPowerBIM_CircuitEditEnhanced _circuitEditEnhancedForm;
        public static PowerBIM_DBData _OLD_DB;

        public static List<PendingData> pendingData = new List<PendingData>();

        #endregion

        #region Properties


        #endregion

        #region Methods
        public static void Initialise_AllCircuits()
        {
            using (var trans_CCT = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans_CCT.Start();
                _OLD_DB.Initialise_AllCircuits();
                trans_CCT.Commit();
            }

        }

        public static void SaveCircuitEditEnhanced()
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                _circuitEditEnhancedForm.SaveCircuitEditEnhanced();
                trans.Commit();
            }
        }

        public static void RevertToOldSave()
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                _circuitEditEnhancedForm.RevertToOldSave();
                trans.Commit();
            }
        }

        public static void AutoCalc()
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                _circuitEditEnhancedForm.AutoCalc();
                trans.Commit();
            }
        }

        static void RecalcAndRefreshCircuitToForm(PendingData c)
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                _circuitEditEnhancedForm.RecalcAndRefreshAllCircuitDataToForm(c.CCT, c.Row);
                trans.Commit();
            }
        }

        public static void MakePendingData()
        {
            foreach (var pendingDataRequest in pendingData)
            {
                switch (pendingDataRequest.requestId)
                {
                    case RequestId.RecalcAndRefreshCircuitToForm:
                        RecalcAndRefreshCircuitToForm(pendingDataRequest);
                        break;
                    case RequestId.RecalcAndRefreshLengthToFormClick:
                        RecalcAndRefreshLengthToFormClick(pendingDataRequest);
                        break;
                    case RequestId.Refresh_DerrivedCircuitProperties:
                        Refresh_DerrivedCircuitProperties(pendingDataRequest);
                        break;
                    case RequestId.WriteLengthAfterUserInputToCircuitParameter:
                        WriteLengthAfterUserInputToCircuitParameter(pendingDataRequest);
                        break;
                    case RequestId.SetCircuitLengthManual:
                        SetCircuitLengthManual(pendingDataRequest);
                        break;
                    case RequestId.SetFirstLengthManual:
                        SetFirstLengthManual(pendingDataRequest);
                        break;
                    case RequestId.SetTotalLengthManual:
                        SetTotalLengthManual(pendingDataRequest);
                        break;
                }
            }

            pendingData.Clear();
        }

        static void RecalcAndRefreshLengthToFormClick(PendingData c)
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                _circuitEditEnhancedForm.RecalcAndRefreshLengthToFormClick(c.CCT, c.Row);
                trans.Commit();
            }
        }

        static void Refresh_DerrivedCircuitProperties(PendingData c)
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                c.CCT.Refresh_DerrivedCircuitProperties();
                trans.Commit();
            }
        }

        static void SetCircuitLengthManual(PendingData c)
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                _circuitEditEnhancedForm.SetCircuitLengthManual(c.CCT, c.Cell);
                trans.Commit();
            }
        }

        static void SetFirstLengthManual(PendingData c)
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                _circuitEditEnhancedForm.SetFirstLengthManual(c.CCT, c.Cell);
                trans.Commit();
            }
        }

        static void SetTotalLengthManual(PendingData c)
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                _circuitEditEnhancedForm.SetTotalLengthManual(c.CCT, c.Cell);
                trans.Commit();
            }
        }

        static void WriteLengthAfterUserInputToCircuitParameter(PendingData c)
        {
            using (var trans = new Transaction(_OLD_DB.Project_Info.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                _circuitEditEnhancedForm.WriteLengthAfterUserInputToCircuitParameter(c.CCT, c.Cell);
                trans.Commit();
            }
        }

        /// <summary>
        ///   Waking up the dialog from its waiting state.
        /// </summary>
        /// 
        public static void WakeFormUpCircuitEditEnhanced()
        {
            if (_circuitEditEnhancedForm != null)
            {
                _circuitEditEnhancedForm.WakeUp();
            }
        }

        public static void OpenPathCustomisingView(out bool viewCreated)
        {
            var changedCells = pendingData.Where(c => c.requestId == RequestId.OpenPathCustomisingView).Last();


            if (changedCells.CCT.LengthClass.OpenPathCustomisingView())
                viewCreated = true;
            else
                viewCreated = false;

        }

        public static void OpenPathCustomisingViewDB(out bool viewCreated)
        {
            if (_OLD_DB.LengthClass.OpenPathCustomisingView())
                viewCreated = true;
            else
                viewCreated = false;
        }

        public static void EnableEditCircuitPath()
        {
            var changedCells = pendingData.Where(c => c.requestId == RequestId.OpenPathCustomisingView).Last();
            changedCells.CCT.projInfo.UIDocument.Selection.SetElementIds(new List<ElementId>(0));//clear any previous selection
            changedCells.CCT.projInfo.UIDocument.Selection.SetElementIds(new List<ElementId>(1) { GetFirstElementIdFromCircuit(changedCells) });//select element
            //pendingData.Remove(changedCells);
        }

        public static void RecalcAndRefreshLengthToForm()
        {
            if (pendingData.Count > 0)
            {
                var c = pendingData.Where(c => c.requestId == RequestId.OpenPathCustomisingView).First();

                pendingData.Add(new PendingData(RequestId.RecalcAndRefreshLengthToFormClick)
                {
                  CCT=  c.CCT,
                  Row=  c.Row
                });
                pendingData.Remove(c);
                _circuitEditEnhancedForm.RecalcAndRefreshLengthToForm();

            }
            else
            {
                return;
            }
        }

        private static ElementId GetFirstElementIdFromCircuit(PendingData c)
        {
            //Get an element id if any element in the circuit 
            var firstElementID = c.CCT.CCT_Electrical_System.Id;

            if (firstElementID == null)
                return ElementId.InvalidElementId;
            else
                return firstElementID;
        }

        public static void ShowMsgToTheUser(string message, string title)
        {
            BecaBaseMessageForm frm = new BecaBaseMessageForm(message, title);
            frm.Show(_circuitEditEnhancedForm);
            frm.BringToFront();
        }

        public static void BringFormsTofront()
        {
            if (_circuitEditEnhancedForm != null)
            {
                _circuitEditEnhancedForm.TopLevel = true;
                _circuitEditEnhancedForm.TopMost = true;

            }
        }
        public static void BringFormsToBack()
        {
            if (_circuitEditEnhancedForm != null)
            {
                _circuitEditEnhancedForm.TopLevel = false;
            }
        }

        #endregion

    }

    #endregion
}