﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using BecaTransactionsNamesManager;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities;
using BecaRevitUtilities.SharedParametersUtilities;
using System.IO;
using System.Reflection;
using Microsoft.Office.Interop.Excel;
using Parameter = Autodesk.Revit.DB.Parameter;
using Common.UI.Forms;
using System.Windows.Forms;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using BecaRevitUtilities.Extensions;

namespace MEP.PowerBIM_5.CoreLogic
{
    public enum VoltDropCalculation
    {
        LinearDeprecating = 1,
        LumpLoad = 2,
        Unknown = 0
    }

    #region Data holding class
    /// <summary>
    /// This class contains information discovered 
    /// about a (shared or non-shared) project parameter 
    /// </summary>
    public class ProjectParameterData
    {
        public Definition Definition = null;
        public ElementBinding Binding = null;
        public string Name = null;                // Needed because accsessing the Definition later may produce an error.
        public bool IsSharedStatusKnown = false;  // Will probably always be true when the data is gathered
        public bool IsShared = false;
        public string GUID = null;
    }


    #endregion

    public class PowerBIM_ProjectInfo
    {
        //
        // BecaLPD_ProjectInfo
        //
        // Simple class holding some of the key parameters we need for the project information.
        //
        //

        #region Fields

        // General Electrical Parameters.
        Parameter paramJobName;
        Parameter paramJobNumber;
        Parameter paramEngineer;
        Parameter paramVerifier;

        // PowerBIM Specific Parameters
        Parameter paramAmbientTemp;
        Parameter paramSystemMaxVDpc;
        Parameter paramDiscrimTestMultiplier;
        Parameter paramCPDRange;
        Parameter paramPBDatabaseLocation;

        Parameter paramPBVDCalcOther;
        Parameter paramPBVDCalcPower;
        Parameter paramPBVDCalcLighting;
        Parameter paramPBGPOCalc;
        Parameter paramPBClearingTimePower;
        Parameter paramPBClearingTimeLighting;

        Parameter paramPBLength_ExtraPerCCT_Other;
        Parameter paramPBLength_ExtraPerElement_Other;
        Parameter paramPBLength_ExtraPerCCT_Lighting;
        Parameter paramPBLength_ExtraPerElement_Lighting;
        Parameter paramPBLength_ExtraPerCCT_Power;
        Parameter paramPBLength_ExtraPerElement_Power;

        Parameter paramPBLength_Beca_Circuit_Length_Manual;
        Parameter paramPBLength_Beca_PB_Revit_Path_Tolerance;

        // For parameter creation
        CategorySet _projectInfoCategorySet;
        CategorySet _projectInfoAndElectricalEquipmentCategorySet;

        // User Interface
        public UIDocument UIDocument { get; set; }
        public Document Document { get; set; }
        public Transaction Active_Trans { get; set; }
        public Transaction Trans_ProtDevice { get; set; }
        public Transaction Trans_Length { get; set; }
        public Transaction Trans_DBCheck { get; set; }
        public BecaActivityLoggerData TaskLogger { get; set; }

        public string CriticalErrorMessage;


        // Project info.
        public string JobName { get; }               // 
        public string JobNumber { get; }             //
        public string Date { get; }                  //
        public string Engineer { get; set; }         //
        public string Verifier { get; set; }         // 
        public string Revision { get; }              // 

        public double Ambient_Temp { get; set; }                        // 
        public double System_VD_Max_Perc { get; set; }                  // 
        public double Discrimination_Test_Multiplier { get; set; }      // 
        public string CPD_Range_Selected { get; set; }
        public string Selected_GPO_Setting { get; set; } //
        public int GPO_Calc_Integer { get; set; }
        public int VD_Calc_Integer { get; set; }
        public int VD_Calc_IntegerPower { get; set; }
        public int VD_Calc_IntegerLighting { get; set; }
        public VoltDropCalculation LightingVDCalculation { get; set; }
        public VoltDropCalculation PowerVDCalculation { get; set; }
        public VoltDropCalculation OtherVDCalculation { get; set; }
        public double Clearing_Time_Power { get; set; }                 // 
        public double Clearing_Time_Lighting { get; set; }              // 
        public bool Parameters_Changed { get; set; }                    //
        public double VoltDropCalculationBreakerRatingPercent { get; set; }                    //

        // Safety factors for length
        public bool Length_ExtraPerElement_enabled { get; set; }
        public bool Length_ExtraPerCCT_enabled { get; set; }
        public double Length_ExtraPerElement_Other { get; set; }
        public double Length_ExtraPerCCT_Other { get; set; }
        public double Length_ExtraPerElement_Lighting { get; set; }
        public double Length_ExtraPerCCT_Lighting { get; set; }
        public double Length_ExtraPerElement_Power { get; set; }
        public double Length_ExtraPerCCT_Power { get; set; }
        public bool Circuit_Length_Manual { get; set; }
        public double NodeCircuitPathTolerance { get; set; }

        // Parameters for report file exprots
        public string File_Base_Info { get; set; }
        public string Folder_Path { get; set; }
        public string Date_Time_Stamp { get; set; }
        public string ReportFile_CableSummary_CSV { get; set; }
        public string ReportFile_VerificationSummary_CSV { get; set; }
        public string ReportFile_CableSummary_XLS { get; set; }
        public string ReportFile_VerificationSummary_XLS { get; set; }
        public string Database_Path { get; set; }
        public string Cable_Database_Name { get; set; }
        public string LockedCircuitInfo_TXT { get; set; }

        #endregion

        // GUI settings selected for running circuit length function
        public double GUI_Calc_Lengths_Extra_Termination { get; set; }


        // GUI settings selected for running circuit check function
        //public bool GUI_CheckDB_Run { get; set; }


        // GUI settings selected for general project settings 
        public int GUI_Gen_CPD_Range_Selected_Index { get; set; }
        public int GUI_Gen_DB_Selected_Position { get; set; }

        //
        public string PathCSVCableData;                  // added cable data
        public string PathCSVSCData;                     // added CS withstand
        public string PathCSVMCBData;                    // add selectivity between MCS brands

        //
        public string[,] Cable_Database = new string[PowerBIM_Constants.file_CableRowsMax, PowerBIM_Constants.file_CableColumnsMax];
        public string[,] K_Value_Database = new string[PowerBIM_Constants.file_KValueRowsMax, PowerBIM_Constants.file_KValueColumnsMax];
        public string[,] CPD_Database = new string[PowerBIM_Constants.file_MCBRowsMax, PowerBIM_Constants.file_MCBColumnsMax];

        public Dictionary<int, string> Standard_CableNames = new Dictionary<int, string> { };

        // Check Database is good
        public bool DatabaseArrayIsPopulated;

        public bool restartFlag;

        //
        // Constructors
        //

        public PowerBIM_ProjectInfo(Autodesk.Revit.UI.UIDocument uidoc, BecaActivityLogger.CoreLogic.Data.BecaActivityLoggerData _taskLogger)
        {
            //
            // BecaLPD_ProjectInfo
            //
            // Constructor populating data from the passed model.
            //
            // TODOs:
            //  1) Add warnings for empty parameters.
            //

            UIDocument = uidoc;
            Document = uidoc.Document;
            Active_Trans = new Transaction(Document);
            Trans_ProtDevice = new Transaction(Document);
            Trans_Length = new Transaction(Document);
            Trans_DBCheck = new Transaction(Document);
            this.TaskLogger = _taskLogger;

            restartFlag = false;

            CriticalErrorMessage = "";

            // Setting the defaults in case the parameters do not exist.
            JobName = "Revit is missing the parameter 'Project Name'";//
            JobNumber = "Revit is missing the parameter 'Project Number'";//
            Engineer = "Revit is missing the parameter '" + PowerBIM_Constants.BecaElectricalEngineer + "'";
            Verifier = "Revit is missing the parameter '" + PowerBIM_Constants.BecaElectricalVerifier + "'";
            Revision = "Please Enter";//

            // Gettings the parameters from Revit.
            paramJobName = Document.ProjectInformation.LookupParameter("Project Name");
            paramJobNumber = Document.ProjectInformation.LookupParameter("Project Number");
            paramEngineer = Document.ProjectInformation.LookupParameter(PowerBIM_Constants.BecaElectricalEngineer);
            paramVerifier = Document.ProjectInformation.LookupParameter(PowerBIM_Constants.BecaElectricalVerifier);

            // Getting the powerBIM specific parameters from revit
            paramAmbientTemp = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidAmbientTemp);
            paramSystemMaxVDpc = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidSystemMaxVDpc);
            paramDiscrimTestMultiplier = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidDiscrimTestMultiplier);
            paramCPDRange = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBCPDManufacturer);
            paramPBDatabaseLocation = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramDatabaseLocation); // TODO
            paramPBLength_Beca_Circuit_Length_Manual = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBLength_Circuit_Length_Manual);

            //
            // Advanced Settings 
            //

            // Check DB parameters
            paramPBVDCalcOther = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBVDCalcOther);
            paramPBVDCalcPower = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBVDCalcPower);
            paramPBVDCalcLighting = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBVDCalcLighting);
            paramPBGPOCalc = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBGPOCalc);
            paramPBClearingTimePower = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBClearingTimePower);
            paramPBClearingTimeLighting = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBClearingTimeLighting);

            paramPBLength_ExtraPerCCT_Other = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerCCT_Other);
            paramPBLength_ExtraPerElement_Other = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerElement_Other);
            paramPBLength_ExtraPerCCT_Lighting = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerCCT_Lighting);
            paramPBLength_ExtraPerElement_Lighting = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerElement_Lighting);
            paramPBLength_ExtraPerCCT_Power = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerCCT_Power);
            paramPBLength_ExtraPerElement_Power = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerElement_Power);

            paramPBLength_Beca_PB_Revit_Path_Tolerance = Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPB_Revit_Path_Tolerance);

            //
            //  ******************* Start error handling *******************************************
            //

            if (paramJobNumber != null)
                JobNumber = paramJobNumber.AsString();
            else
                ;//log?.Warning("Revit project is missing the parameter 'Project Number'.");

            if (paramEngineer != null)
                Engineer = paramEngineer.AsString();
            else
                ;//log?.Warning("Revit project is missing the parameter '" + PowerBIM_Constants.BecaElectricalEngineer + "'.");

            if (paramVerifier != null)
                Verifier = paramVerifier.AsString();
            else
                ;//log?.Warning("Revit project is missing the parameter '" + PowerBIM_Constants.BecaElectricalVerifier + "'.");

            Date = DateTime.Now.ToString("dd/MM/yy");



            Parameters_Changed = false;

            // CategorySet for parameter creation
            _projectInfoCategorySet = new CategorySet();
            _projectInfoAndElectricalEquipmentCategorySet = new CategorySet();
            foreach (Category c in Document.Settings.Categories)
            {
                if (c.AllowsBoundParameters && (BuiltInCategory)c.Id.IntegerValue() == BuiltInCategory.OST_ProjectInformation)
                {
                    _projectInfoCategorySet.Insert(c);
                    _projectInfoAndElectricalEquipmentCategorySet.Insert(c);
                }
                else if (c.AllowsBoundParameters && (BuiltInCategory)c.Id.IntegerValue() == BuiltInCategory.OST_ElectricalEquipment)
                {
                    _projectInfoAndElectricalEquipmentCategorySet.Insert(c);
                }

            }

            #region circuit param

            //CheckParameter(PowerBIM_Constants.paramRevitCircuitManualCurrent, PowerBIM_Constants.PB_Current_ManualName);

            //CheckParameter(PowerBIM_Constants.paramRevitCircuitManualCurrentValue, PowerBIM_Constants.PB_CurrentName);

            #endregion
            //
            // ADVANCED SETTINGS
            //
            // Check if paramter exists, and if it does exist, check that it has been assigned in the project.. otherwise set to default value
            //
            // If VD_Calc_Integer has been assigned to project
            CheckParameter(paramPBVDCalcOther, PowerBIM_Constants.PBVDCalcName, nameof(this.VD_Calc_Integer), PowerBIM_Constants.Default_PBVDCalc);
            // If VD_Calc_Power has been assigned to project
            CheckParameter(paramPBVDCalcPower, PowerBIM_Constants.PBVDCalcPowerName, nameof(this.VD_Calc_IntegerPower), PowerBIM_Constants.Default_PBVDCalcPower);
            // If VD_Calc_IntegerLighting has been assigned to project
            CheckParameter(paramPBVDCalcLighting, PowerBIM_Constants.PBVDCalcLightingName, nameof(this.VD_Calc_IntegerLighting), PowerBIM_Constants.Default_PBVDCalcLighting);
            // If GPO calc integer has been assigned in the project, use that. Otherwise use default.
            CheckParameter(paramPBGPOCalc, PowerBIM_Constants.PB_GPO_CalcName, nameof(this.GPO_Calc_Integer), PowerBIM_Constants.Default_PBGPOCalc);
            // If paramPBClearingTimeLighting is assigned in project, use that. Otherwsie, use default
            CheckParameter(paramPBClearingTimeLighting, PowerBIM_Constants.PBClearingTimeLightingName, nameof(this.Clearing_Time_Lighting), PowerBIM_Constants.Default_ClearingTimeLighting);
            // If paramPBClearingTimePower is assigned in project, use that. Otherwsie, use default
            CheckParameter(paramPBClearingTimePower, PowerBIM_Constants.PBClearingTimePowerName, nameof(this.Clearing_Time_Power), PowerBIM_Constants.Default_ClearingTimePower);

            // If Length_ExtraPerCCT_Lighting has been assigned to project
            CheckParameter(paramPBLength_ExtraPerCCT_Lighting, PowerBIM_Constants.PB_Additional_Length_Per_Circuit_LightingName, nameof(this.Length_ExtraPerCCT_Lighting), PowerBIM_Constants.Default_Length_ExtraPerCCT_Lighting);
            // If Length_ExtraPerElement_Lighting has been assigned to project
            CheckParameter(paramPBLength_ExtraPerElement_Lighting, PowerBIM_Constants.PB_Additional_Length_Per_Elem_LightingName, nameof(this.Length_ExtraPerElement_Lighting), PowerBIM_Constants.Default_Length_ExtraPerElement_Lighting);
            // If Length_ExtraPerCCT_Power has been assigned to project
            CheckParameter(paramPBLength_ExtraPerCCT_Power, PowerBIM_Constants.PB_Additional_Length_Per_Circuit_PowerName, nameof(this.Length_ExtraPerCCT_Power), PowerBIM_Constants.Default_Length_ExtraPerCCT_Power);
            // If Length_ExtraPerElement_Power has been assigned to project
            CheckParameter(paramPBLength_ExtraPerElement_Power, PowerBIM_Constants.PB_Additional_Length_Per_Elem_PowerName, nameof(this.Length_ExtraPerElement_Power), PowerBIM_Constants.Default_Length_ExtraPerElement_Power);
            // If Length_ExtraPerCCT_Other has been assigned to project
            CheckParameter(paramPBLength_ExtraPerCCT_Other, PowerBIM_Constants.PB_Additional_Length_Per_CircuitName, nameof(this.Length_ExtraPerCCT_Other), PowerBIM_Constants.Default_Length_ExtraPerCCT_Other);
            // If Length_ExtraPerElement_Other has been assigned to project
            CheckParameter(paramPBLength_ExtraPerElement_Other, PowerBIM_Constants.PB_Additional_Length_Per_ElemName, nameof(this.Length_ExtraPerElement_Other), PowerBIM_Constants.Default_Length_ExtraPerElement_Other);

            // If NodeCircuitPathTolerance has been assigned to project
            CheckParameter(paramPBLength_Beca_PB_Revit_Path_Tolerance, PowerBIM_Constants.PB_Revit_Path_ToleranceName, nameof(this.NodeCircuitPathTolerance), PowerBIM_Constants.Default_Length_PathTolerance);
            // If CPD range is assigned in project, use that. Otherwsie, use default
            CheckParameter(paramCPDRange, PowerBIM_Constants.CPDRangeName, nameof(this.CPD_Range_Selected), PowerBIM_Constants.Default_CPDRange);
            // If PB_Max_VD_Perc is assigned in project, use that. Otherwsie, use default
            CheckParameter(paramSystemMaxVDpc, PowerBIM_Constants.SystemMaxVDpcName, nameof(this.System_VD_Max_Perc), PowerBIM_Constants.Default_SystemMaxVoltDrop);
            // If paramDiscrimTestMultiplier is assigned in project, use that. Otherwsie, use default
            CheckParameter(paramDiscrimTestMultiplier, PowerBIM_Constants.DiscrimTestMultiplierName, nameof(this.Discrimination_Test_Multiplier), PowerBIM_Constants.Default_DiscriminationMultiplier);

            //
            if (paramAmbientTemp != null)
            {
                // Check that the value has been asigned (0 = unassigned)
                if (paramAmbientTemp.AsDouble() == 0.0)
                {
                    Ambient_Temp = PowerBIM_Constants.Default_AmbientTemp;
                    Cable_Database_Name = PowerBIM_Constants.Default_CableDatabase;
                }
                else
                {
                    Ambient_Temp = paramAmbientTemp.AsDouble() - 273.15;

                    // If temp is sotred as 40deg then set PowerBIM to use the Aussie cable dabase
                    if (Ambient_Temp == 40)
                        Cable_Database_Name = "Cable Database AU";
                    // Otherwise use NZ
                    else
                        Cable_Database_Name = "Cable Database NZ";
                }
            }
            else
                CriticalErrorMessage += "Revit project is missing the parameter '" + "Ambient_Temperature" + "'. \n";

            //
            if (paramPBDatabaseLocation != null)
            {
                // Check that the value has been asigned (0 = unassigned)
                if (!paramPBDatabaseLocation.HasValue)
                {
                    Database_Path = PowerBIM_Constants.Default_CableDatabaseLocation;
                    SetDatabasePathParameter();
                }
                else
                {
                    Database_Path = paramPBDatabaseLocation.AsString();
                }

            }
            else
                // we havn't added this parameter yet, so we will always use this defalt location.
                Database_Path = PowerBIM_Constants.Default_CableDatabaseLocation; //TODO

            if (!Directory.Exists(Database_Path))
            {
              
                BecaBaseMessageForm frm = new BecaBaseMessageForm("Unable to load project specific cable database (the file may have been moved or deleted). PowerBIM will use your default database instead." + Environment.NewLine
                    + "project specific cable database : " + Database_Path, "Invalid project specific cable database");

                frm.ShowDialog();
                Database_Path = PowerBIM_Constants.Default_CableDatabaseLocation; //TODO
            }

            GetDateTimeInfo();
            UpdateFilePaths();

            DatabaseArrayIsPopulated = PopulateDatabaseArrays();

            //SetAdvancedSettingsParameter();

            CleanUpElectricalCircuitingParameters(Document);

            // Check VD Calcs and set it with default value from property when it's 0 in revit (case of old project)
            CheckDefaultVDParameterValue(paramPBVDCalcPower, VD_Calc_IntegerPower);
            CheckDefaultVDParameterValue(paramPBVDCalcLighting, VD_Calc_IntegerLighting);
            CheckDefaultVDParameterValue(paramPBVDCalcOther, VD_Calc_Integer);

            // Read Volt Drop values from Revit parameters
            PowerVDCalculation = GetIntParamValueValueOrDefault<VoltDropCalculation>(paramPBVDCalcPower);
            LightingVDCalculation = GetIntParamValueValueOrDefault<VoltDropCalculation>(paramPBVDCalcLighting);
            OtherVDCalculation = GetIntParamValueValueOrDefault<VoltDropCalculation>(paramPBVDCalcOther);

        }

        private T GetIntParamValueValueOrDefault<T>(Parameter param) where T : struct
        {
            return param != null && Enum.TryParse<T>(param.AsInteger().ToString(), out T result) ? result : default;
        }

        /// <summary>
        /// Returns a list of the objects containing 
        /// references to the project parameter definitions
        /// </summary>
        /// <param name="doc">The project document being quereied</param>
        /// <returns></returns>
        private void CleanUpElectricalCircuitingParameters(Document doc)
        {
            try
            {
                using (var trans = new Transaction(doc, "Check parameter"))
                {
                    trans.Start();
                    // CategorySet for parameter creation
                    var _projectInfoAndElectricalEquipmentCategorySet = new CategorySet();
                    _projectInfoAndElectricalEquipmentCategorySet.Insert(Category.GetCategory(doc, BuiltInCategory.OST_ProjectInformation));
                    _projectInfoAndElectricalEquipmentCategorySet.Insert(Category.GetCategory(doc, BuiltInCategory.OST_ElectricalEquipment));

                    BindingMap map = doc.ParameterBindings;
                    DefinitionBindingMapIterator iterator = map.ForwardIterator();
                    iterator.Reset();
                    while (iterator.MoveNext())
                    {
                        if (!IsParameterNameMatched(iterator.Key.Name))
                        {
                            continue;
                        }

                        var reInsert = false;
                        var categorySet = (iterator.Current as ElementBinding).Categories;

                        // Remove wrong categories
                        foreach (Category c in categorySet)
                        {
                            if (!_projectInfoAndElectricalEquipmentCategorySet.Contains(c))
                            {
                                categorySet.Erase(c);
                                reInsert = true;
                            }
                        }
                        // Add missing categories
                        foreach (Category c in _projectInfoAndElectricalEquipmentCategorySet)
                        {
                            if (!categorySet.Contains(c))
                            {
                                categorySet.Insert(c);
                                reInsert = true;
                            }
                        }

#if TargetYear2022 || TargetYear2023 || TargetYear2024 || TargetYear2025 || TargetYear2026

                        // Category is different do reinserting
                        if (reInsert)
                        {
                            var dataType = iterator.Key.GetDataType();
                            if (dataType == SpecTypeId.Int.Integer)
                            {
                                CacheAndReInsertParameterAndValues<int>(doc, iterator.Key, categorySet);
                            }
                            else if (dataType == SpecTypeId.Number)
                            {
                                CacheAndReInsertParameterAndValues<double>(doc, iterator.Key, categorySet);
                            }
                            else if (dataType == SpecTypeId.String.Text)
                            {
                                CacheAndReInsertParameterAndValues<string>(doc, iterator.Key, categorySet);
                            }
                        }
#else
                    // Category is different do reinserting
                    if (reInsert)
                    {
                        switch (iterator.Key.ParameterType)
                        {
                            case ParameterType.Integer:
                                CacheAndReInsertParameterAndValues<int>(doc, iterator.Key, categorySet);
                                break;
                            case ParameterType.Number:
                                CacheAndReInsertParameterAndValues<double>(doc, iterator.Key, categorySet);
                                break;
                            case ParameterType.Text:
                                CacheAndReInsertParameterAndValues<string>(doc, iterator.Key, categorySet);
                                break;
                        }
                    }
#endif

                    }

                    trans.Commit();
                }
            }
            catch (Exception e )
            {
                MessageBox.Show($"Error while cleaning up electrical circuiting parameters: {e.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            

        }

        private void CheckDefaultVDParameterValue(Parameter param, int intValue)
        {
            if (param.AsInteger() == 0)
            {
                using (var trans = new Transaction(Document, "Set VDCalc"))
                {
                    trans.Start();
                    param.Set(intValue);
                    trans.Commit();
                }
            }
        }

        private bool IsParameterNameMatched(string parameterName)
        {
            var matchedParameterNames = new string[]
            {
                PowerBIM_Constants.PBVDCalcName,
                PowerBIM_Constants.PBVDCalcPowerName,
                PowerBIM_Constants.PBVDCalcLightingName,
                PowerBIM_Constants.PB_GPO_CalcName,
                PowerBIM_Constants.PBClearingTimeLightingName,
                PowerBIM_Constants.PBClearingTimePowerName,
                PowerBIM_Constants.PB_Additional_Length_Per_Circuit_LightingName,
                PowerBIM_Constants.PB_Additional_Length_Per_Elem_LightingName,
                PowerBIM_Constants.PB_Additional_Length_Per_Circuit_PowerName,
                PowerBIM_Constants.PB_Additional_Length_Per_Elem_PowerName,
                PowerBIM_Constants.PB_Additional_Length_Per_CircuitName,
                PowerBIM_Constants.PB_Additional_Length_Per_ElemName
            };

            return matchedParameterNames.Contains(parameterName);
        }

        private void CacheAndReInsertParameterAndValues<T>(Document doc, Definition definition, CategorySet categorySet)
        {
            var parameterCache = new ParameterValueCache<T>(doc, definition, categorySet);

            parameterCache.CacheValues();
#if TargetYear2024 || TargetYear2025 || TargetYear2026
            doc.ParameterBindings.ReInsert(definition, doc.Application.Create.NewInstanceBinding(categorySet), GroupTypeId.ElectricalCircuiting);
#else
            doc.ParameterBindings.ReInsert(definition, doc.Application.Create.NewInstanceBinding(categorySet), BuiltInParameterGroup.PG_ELECTRICAL_CIRCUITING);
#endif
            parameterCache.ReInsertValues();
        }


        /// <summary>
        /// Check parameter and create one when its null
        /// </summary>
        /// <param name="paramRevitCircuitManualCurrentValue"></param>
        /// <param name="parameterName"></param>
        private void CheckParameter(Guid paramRevitCircuitManualCurrentValue, string parameterName)
        {
            //            Parameter circuitParam = Document.ProjectInformation.get_Parameter(paramRevitCircuitManualCurrentValue);
            //            if (circuitParam == null)
            //            {
            //                CriticalErrorMessage += "Revit project is missing the parameter '" + parameterName + "'. \n";

            //                if (ParameterTaskDialog(parameterName))
            //                {
            //#if TargetYear2023
            //                    CreateParameter(parameterName);
            //#else
            //                    CreateParameter(parameterName);
            //#endif

            //                    restartFlag = true;
            //                }
            //            }
        }

        /// <summary>
        /// Checks parameter, set value when it exists and create one when its null
        /// </summary>
        /// <param name="parameterToCheck"></param>
        /// <param name="parameterName"></param>
        /// <param name="propertyName"></param>
        /// <param name="valueToSet"></param>
        public void CheckParameter(Parameter parameterToCheck, string parameterName, string propertyName, object valueToSet)
        {
            var field = GetType().GetProperty(propertyName, BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.FlattenHierarchy);

            if (parameterToCheck != null)
            {
                switch (field.PropertyType)
                {
                    case Type t when t == typeof(int):
                        // Check that the value has been assigned (0 = unassigned)
                        if (parameterToCheck.AsInteger() == 0)
                            field.SetValue(this, Convert.ChangeType(valueToSet, field.PropertyType));
                        else
                            field.SetValue(this, parameterToCheck.AsInteger());
                        break;
                    case Type t when t == typeof(double):
                        if (parameterToCheck.AsDouble() == 0)
                            field.SetValue(this, Convert.ChangeType(valueToSet, field.PropertyType));
                        else
                            field.SetValue(this, parameterToCheck.AsDouble());
                        break;
                    case Type t when t == typeof(string):
                        if (parameterToCheck.HasValue == false)
                            field.SetValue(this, Convert.ChangeType(valueToSet, field.PropertyType));
                        else
                        {
                            if (!string.IsNullOrEmpty(parameterToCheck.AsString()))
                            {
                                field.SetValue(this, parameterToCheck.AsString());
                            }
                            else
                            {
                                field.SetValue(this, parameterToCheck.AsValueString());
                            }
                        }

                        break;
                    default:
                        break;
                }
            }
            else
            {
                CriticalErrorMessage += $"Revit project is missing the parameter '{parameterName}'. \n";

                CreateParameter(parameterName);
                restartFlag = true;
            }
        }

        private void CreateParameter(string parameterName)
        {
            CategorySet ct = _projectInfoCategorySet;
            if (parameterName == PowerBIM_Constants.PBVDCalcName || parameterName == PowerBIM_Constants.PBVDCalcPowerName || parameterName == PowerBIM_Constants.PBVDCalcLightingName ||
                parameterName == PowerBIM_Constants.PB_GPO_CalcName || parameterName == PowerBIM_Constants.PBClearingTimeLightingName || parameterName == PowerBIM_Constants.PBClearingTimePowerName ||
                parameterName == PowerBIM_Constants.PB_Additional_Length_Per_Circuit_LightingName || parameterName == PowerBIM_Constants.PB_Additional_Length_Per_Elem_LightingName || parameterName == PowerBIM_Constants.PB_Additional_Length_Per_Circuit_PowerName ||
                parameterName == PowerBIM_Constants.PB_Additional_Length_Per_Elem_PowerName || parameterName == PowerBIM_Constants.PB_Additional_Length_Per_CircuitName || parameterName == PowerBIM_Constants.PB_Additional_Length_Per_ElemName) 
                ct = _projectInfoAndElectricalEquipmentCategorySet;

            var sharedParameterFilePath = PowerBIM_Constants.SharedParameterPath + "\\Beca_77_ELEC_Shared_Parameters.txt";
            using (var trans = new Transaction(Document, "Create Parameter"))
            {
                trans.Start();
                try
                {
#if TargetYear2025 || TargetYear2026
                    if (!SharedParameterUtility.AddProjectParameter(Document, Document.Application, parameterName, ct, GroupTypeId.ElectricalCircuiting, true, sharedParameterFilePath))
                        CriticalErrorMessage += "Revit project is missing the parameter '" + parameterName + "'. \n";
#else
                    if (!SharedParameterUtility.AddProjectParameter(Document, Document.Application, parameterName, ct, BuiltInParameterGroup.PG_ELECTRICAL_CIRCUITING, true, sharedParameterFilePath))
                                            CriticalErrorMessage += "Revit project is missing the parameter '" + parameterName + "'. \n";
#endif
                }
                catch (Exception ex)
                {
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Parameter creation exception", ex.Message + "\n\nParameter name: " + parameterName);
                }
                trans.Commit();
            }
        }


        public bool ParameterTaskDialog(string parameterName)
        {
            TaskDialog dialog = new TaskDialog("Parameter check");
            dialog.MainContent = "Revit project is missing the parameter '" + parameterName + "'. \n\nDo you wish to create them now?";
            dialog.AllowCancellation = true;
            dialog.CommonButtons = TaskDialogCommonButtons.Yes | TaskDialogCommonButtons.No;


            TaskDialogResult result = dialog.Show();
            if (result == TaskDialogResult.Yes)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        #region Public Methods

        public void CommitToRevit()
        {
            //
            // CommitToRevit
            //
            // Commits any updates to the parameters provided they are non-null.
            //
            // TODOs
            //  1) Probably should only commit if something has actually changed.
            //

            // Transaction wrapper.
            Transaction activeTrans = new Transaction(Document);
            activeTrans.Start("Update Project Global Settings.");

            // Setting parameter values.
            paramJobName?.Set(JobName);
            paramJobNumber?.Set(JobNumber);
            paramEngineer?.Set(Engineer);
            paramVerifier?.Set(Verifier);

            // Committing the values.
            activeTrans.Commit();

            return;
        }

        public void GetDateTimeInfo()
        {
            //----- File Base Information the -----
            string UserLogin = System.Security.Principal.WindowsIdentity.GetCurrent().Name.Split('\\').Last();
            string StrDateTime = DateTime.Now.ToString("dd/MM/yy H:mm:ss");
            string StrRevitFile = Document.PathName;
            string StrFileBaseInfo = StrRevitFile + "  ***  " + StrDateTime + "  ***  " + "by: " + UserLogin + "  ***  " + PowerBIM_Constants.Copyright;

            // Setup file location in temp directory
            string Folder = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            Folder = Folder + @"\Beca MEP Tools\";
            if (!System.IO.Directory.Exists(Folder))
                System.IO.Directory.CreateDirectory(Folder);

            Folder = Folder + @"PowerBIM Reports\";
            if (!System.IO.Directory.Exists(Folder))
                System.IO.Directory.CreateDirectory(Folder);

            string RvtTitle = Document.Title;
            RvtTitle = RvtTitle.Substring(0, RvtTitle.Length - 4);
            Folder = Folder + RvtTitle + @"\";
            if (!System.IO.Directory.Exists(Folder))
                System.IO.Directory.CreateDirectory(Folder);

            // Save to projectinfo class public parameters
            Folder_Path = Folder;
            File_Base_Info = StrFileBaseInfo;
            Date_Time_Stamp = DateTime.Now.ToString("yyMMdd") + "_" + DateTime.Now.ToString("HHmmss");

            // Create output file names and paths
            ReportFile_CableSummary_CSV = Folder + RvtTitle + "_" + Date_Time_Stamp + " - Cable Summary Report" + ".csv";
            ReportFile_VerificationSummary_CSV = Folder + RvtTitle + "_" + Date_Time_Stamp + " - Verification Summary Report" + ".csv";
            ReportFile_CableSummary_XLS = Folder + RvtTitle + "_" + Date_Time_Stamp + " - DB Schedules" + ".xlsx";
            ReportFile_VerificationSummary_XLS = Folder + RvtTitle + "_" + Date_Time_Stamp + " - DB SChedules with Verification Pane" + ".xlsx";
            LockedCircuitInfo_TXT = Folder + RvtTitle + " - Locked circuit info" + ".txt";
        }

        public bool PopulateDatabaseArrays()
        {
            try
            {
                //Read Cable Database from CSV
                if (!Common.Utilities.CSVUtility.WriteTo2DArray(ref Cable_Database, PathCSVCableData, 0, PowerBIM_Constants.file_CableColumnsMax))
                {
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Critical Error", "Unable to read cable database file. Please check that your PowerBIM 5 database files exist!");
                    return false;
                }

                //Read SC Data from CSV
                if (!Common.Utilities.CSVUtility.WriteTo2DArray(ref K_Value_Database, PathCSVSCData, 0, PowerBIM_Constants.file_KValueColumnsMax))
                {
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Critical Error", "Unable to read k-value database file. Please check that your PowerBIM 5 database files exist!");
                    return false;
                }

                //Read MCB Data from CSV
                if (!Common.Utilities.CSVUtility.WriteTo2DArray(ref CPD_Database, PathCSVMCBData, 0, PowerBIM_Constants.file_MCBColumnsMax))
                {
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Critical Error", "Unable to read CPD database file. Please check that your PowerBIM 5 database files exist!");
                    return false;
                }

                // While we're here, populate dynamic dictionary for cable names
                BuildCableNameList();
                return true;
            }
            catch
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Critical Error", "\nUnable to run PowerBIM. Please check that your PowerBIM 5 database files exist!");
                return false;
            }

        }

        //public void PopulateDatabaseArrays()
        //{
        //    try
        //    {
        //        //Read Cable Database from CSV
        //        if (!Common.Utilities.CSVUtility.WriteTo2DArray(ref Cable_Database, PathCSVCableData, 0, PowerBIM_Constants.file_CableColumnsMax))
        //            TaskDialog.Show("Critical Error", "Unable to read cable database file. Please check that your PowerBIM 5 database files exist!");

        //        //Read SC Data from CSV
        //        if (!Common.Utilities.CSVUtility.WriteTo2DArray(ref K_Value_Database, PathCSVSCData, 0, PowerBIM_Constants.file_KValueColumnsMax))
        //            TaskDialog.Show("Critical Error", "Unable to read k-value database file. Please check that your PowerBIM 5 database files exist!");

        //        //Read MCB Data from CSV
        //        if (!Common.Utilities.CSVUtility.WriteTo2DArray(ref CPD_Database, PathCSVMCBData, 0, PowerBIM_Constants.file_MCBColumnsMax))
        //            TaskDialog.Show("Critical Error", "Unable to read CPD database file. Please check that your PowerBIM 5 database files exist!");

        //        // While we're here, populate dynamic dictionary for cable names
        //        BuildCableNameList();
        //    }
        //    catch
        //    {
        //        TaskDialog.Show("Critical Error", "\nUnable to run PowerBIM. Please check that your PowerBIM 5 database files exist!");
        //    }

        //}

        public void CommitProjectInfo()
        {
            if (Parameters_Changed)
            {
                // write parameters back to revit
                paramAmbientTemp.Set(Ambient_Temp + PowerBIM_Constants.TemperatureInKelvin);
                paramSystemMaxVDpc.Set(System_VD_Max_Perc);
                paramDiscrimTestMultiplier.Set(Discrimination_Test_Multiplier);
                paramCPDRange.Set(GUI_Gen_CPD_Range_Selected_Index);
            }
        }

        public void Commit_AdvancedSettings()
        {
            if (Parameters_Changed)
            {
                SetAdvancedSettingsParameter();
            }
        }

        public void SetDatabasePathParameter()
        {
            using (var trans = new Transaction(Document, "Set default PB database"))
            {
                trans.Start();
                paramPBDatabaseLocation.Set(Database_Path);
                trans.Commit();
            }
        }

        public void SetAdvancedSettingsParameter()
        {
            // Open a new transaction
            Active_Trans.Start(BecaTransactionsNames.PowerBIM_UpdateAdvancedSettings.GetHumanReadableString());

            // Set relevent parameters
            paramPBVDCalcOther.Set((int)OtherVDCalculation);
            paramPBVDCalcPower.Set((int)PowerVDCalculation);
            paramPBVDCalcLighting.Set((int)LightingVDCalculation);
            paramPBGPOCalc.Set(GPO_Calc_Integer);
            paramPBClearingTimePower.Set(Clearing_Time_Power);
            paramPBClearingTimeLighting.Set(Clearing_Time_Lighting);

            paramPBLength_ExtraPerElement_Lighting.Set(Length_ExtraPerElement_Lighting);
            paramPBLength_ExtraPerCCT_Lighting.Set(Length_ExtraPerCCT_Lighting);
            paramPBLength_ExtraPerElement_Power.Set(Length_ExtraPerElement_Power);
            paramPBLength_ExtraPerCCT_Power.Set(Length_ExtraPerCCT_Power);
            paramPBLength_ExtraPerElement_Other.Set(Length_ExtraPerElement_Other);
            paramPBLength_ExtraPerCCT_Other.Set(Length_ExtraPerCCT_Other);

            paramPBLength_Beca_PB_Revit_Path_Tolerance.Set(NodeCircuitPathTolerance);

            // Close the transaction
            Active_Trans.Commit();
        }



        public void UpdateFilePaths()
        {
            // added cable data
            PathCSVCableData = Database_Path + @"\" + Cable_Database_Name + ".csv";

            // added CS withstand
            PathCSVSCData = Database_Path + @"\" + PowerBIM_Constants.DatabaseName_SCWithstand;

            // add selectivity between MCS brands
            if (paramCPDRange != null && paramCPDRange.HasValue && paramCPDRange.AsInteger() != 0)
                PathCSVMCBData = Database_Path + @"\MCB - " + PowerBIM_Constants.strCPDRangeList[paramCPDRange.AsInteger()] + ".csv";
            else
                PathCSVMCBData = Database_Path + @"\MCB - " + PowerBIM_Constants.strCPDRangeList[2] + ".csv";
        }

        public void UpdateAllProjectInfo()
        {
            // First, update the file paths incase the user has changed database locations
            UpdateFilePaths();

            // Next, regenrate the database arrrays
            DatabaseArrayIsPopulated = PopulateDatabaseArrays();

            // Now, commit project infor set in the main GUI for next time.
            CommitProjectInfo();
        }

        public void BuildCableNameList()
        {
            if (Standard_CableNames.Count > 0)
                return;

            // Add cable names from the database, skipping null/empty names
            for (int i = 0; i < PowerBIM_Constants.file_CableRowsMax; i++)
            {
                var cableName = Cable_Database[i, 12];
                if (!string.IsNullOrWhiteSpace(cableName))
                {
                    Standard_CableNames[i] = cableName;
                }
            }

            // Add special entries
            int nextIndex = PowerBIM_Constants.file_CableRowsMax;
            Standard_CableNames[nextIndex++] = "";
            Standard_CableNames[nextIndex++] = PowerBIM_CableData.InvalidCableName;
            Standard_CableNames[nextIndex++] = "-";
            //Standard_CableNames[nextIndex++] = "REFER TO SCHEDULE";
        }
        #endregion
    }

}
