﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using System.Windows.Forms;
using Autodesk.Revit.UI;
using Common.UI.Forms;
using BecaTransactionsNamesManager;
using BecaRevitUtilities.ElectricalUtilities;
using BecaRevitUtilities;
using BecaRevitUtilities.SharedParametersUtilities;
using MEP.PowerBIM_5.UI.Forms;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class PowerBIM_DBData
    {
        #region Fields
        Parameter _dbParamPBVDCalcOther;
        Parameter _dbParamPBVDCalcPower;
        Parameter _dbParamPBVDCalcLighting;
        Parameter _dbParamPBGPOCalc;
        Parameter _dbParamPBClearingTimePower;
        Parameter _dbParamPBClearingTimeLighting;

        Parameter _dbParamPBLength_ExtraPerCCT_Other;
        Parameter _dbParamPBLength_ExtraPerElement_Other;
        Parameter _dbParamPBLength_ExtraPerCCT_Lighting;
        Parameter _dbParamPBLength_ExtraPerElement_Lighting;
        Parameter _dbParamPBLength_ExtraPerCCT_Power;
        Parameter _dbParamPBLength_ExtraPerElement_Power;
        #endregion

        public PowerBIM_ProjectInfo Project_Info { get; set; }
        public Element DB_Element { get; set; }
        public ElectricalSystem DB_ElectricalSystem { get; set; }
        public PowerBIM_DBRoute LengthClass { get; set; }

        public List<PowerBIM_CircuitData> CCTs { get; set; }
        public XYZ DB_Location { get; set; }

        public string CircuitNaming { get; set; }

        public string Schedule_DB_Name { get; set; }
        public double Schedule_Main_Switch_Rating { get; set; }
        public string Schedule_Feeder_Cable { get; set; }
        public string Schedule_Location { get; set; }
        public string Schedule_Seismic_Category { get; set; }
        public string Schedule_Device_Fault_Rating { get; set; }
        public string Schedule_Form_Rating { get; set; }
        public string Schedule_Bus_Fault_Level { get; set; }
        public string Schedule_Surge_Protection { get; set; }
        public string Schedule_Metering { get; set; }
        public double Schedule_Final_Circuit_MaxVD { get; set; }

        public double Upstream_Device_Rating { get; set; }
        public double Device_kA_Rating { get; set; }
        public double EFLI_R { get; set; }
        public double EFLI_X { get; set; }
        public double DBVD { get; set; }
        public double PSCC { get; set; }

        public double RevitDiversifiedTotalPhaseCurrentR { get; set; }
        public double RevitDiversifiedTotalPhaseCurrentW { get; set; }
        public double RevitDiversifiedTotalPhaseCurrentB { get; set; }

        public PanelScheduleView PanelScheduleView { get; set; }
        public int NumberOfWays { get; set; }

        public bool Data_Good { get; set; }
        public bool Parameters_Missing { get; set; }
        public int Result_PassCount { get; set; }
        public int Result_WarningCount { get; set; }
        public int Result_FailCount { get; set; }
        public string User_Notes { get; set; }
        public string GUI_Notes { get; set; }
        public string GUI_Notes_Message { get; set; }
        public bool Update_Required { get; set; }



        public bool DB_All_Circuits_Pass { get; set; }

        public string DB_Check_Result { get; set; }
        public string DB_Check_Warnings { get; set; }
        public string DB_Result_Summary { get; set; }

        public double Diversified_Total_Phase_Current_R { get; set; }
        public double Diversified_Total_Phase_Current_W { get; set; }
        public double Diversified_Total_Phase_Current_B { get; set; }
        public List<PowerBIM_CircuitData> LockedCircuits { get; set; }
        public string IsLocked { get; set; }
        public bool IsManuallyLocked { get; set; }

        // Check Custom Path
        public bool HasCustomCircuitPath { get { return DB_ElectricalSystem == null ? false : DB_ElectricalSystem.HasCustomCircuitPath; } }

        // From DB Settings
        public Transaction Active_Trans { get; set; }
        public int GPO_Calc_Integer { get; set; }
        public double VoltDropCalculationBreakerRatingPercent { get; set; }
        public bool Length_ExtraPerElement_enabled { get; set; }
        public bool Length_ExtraPerCCT_enabled { get; set; }
        public int VD_Calc_Integer { get; set; }
        public int VD_Calc_IntegerPower { get; set; }
        public int VD_Calc_IntegerLighting { get; set; }
        public VoltDropCalculation LightingVDCalculation { get; set; }
        public VoltDropCalculation PowerVDCalculation { get; set; }
        public VoltDropCalculation OtherVDCalculation { get; set; }
        public double Clearing_Time_Power { get; set; }
        public double Clearing_Time_Lighting { get; set; }
        public double Length_ExtraPerElement_Other { get; set; }
        public double Length_ExtraPerCCT_Other { get; set; }
        public double Length_ExtraPerElement_Lighting { get; set; }
        public double Length_ExtraPerCCT_Lighting { get; set; }
        public double Length_ExtraPerElement_Power { get; set; }
        public double Length_ExtraPerCCT_Power { get; set; }
        public bool Parameters_Changed { get; set; }

        public double UnDiversified_Total_Phase_Current_R { get; set; }
        public double UnDiversified_Total_Phase_Current_W { get; set; }
        public double UnDiversified_Total_Phase_Current_B { get; set; }


        //Get DB Parameters
        #region Parameters
        private Parameter paramMansSWrating;
        private Parameter paramDBFeederCable;
        private Parameter paramDBLocation;
        private Parameter paramDBSeismicCategory;
        private Parameter paramDeviceFaultRating;
        private Parameter paramDBFormRating;
        private Parameter paramDBBusFaultLevel;
        private Parameter paramDBSurgeProtection;
        private Parameter paramDBMetering;
        private Parameter paramDBfinalCCTMaxVDPC;

        private Parameter paramDBEFLiR;
        private Parameter paramDBEFLiX;
        private Parameter paramDBVD;
        private Parameter paramDBPSCC;
        private Parameter paramUpstreamRating;
        private Parameter paramDBchk;

        private Parameter paramRevitDiversifiedTotalPhaseCurrentR;
        private Parameter paramRevitDiversifiedTotalPhaseCurrentW;
        private Parameter paramRevitDiversifiedTotalPhaseCurrentB;

        internal Parameter paramPBLength_Beca_Circuit_Length_Manual;

        #endregion


        public PowerBIM_DBData(PowerBIM_ProjectInfo pi, Element db)
        {
            //
            // BecaDB_Data
            //
            // Constructor populating data from the passed DB
            //

            Project_Info = pi;

            Active_Trans = new Transaction(pi.Document);

            //Get DB Parameters
            paramMansSWrating = db.get_Parameter(BuiltInParameter.RBS_ELEC_MAINS);
            paramDBFeederCable = db.get_Parameter(PowerBIM_Constants.paramGuidDBFeederCable);
            paramDBLocation = db.get_Parameter(PowerBIM_Constants.paramGuidDBLocation);
            paramDBSeismicCategory = db.get_Parameter(PowerBIM_Constants.paramGuidDBSeismicCategory);
            paramDeviceFaultRating = db.get_Parameter(BuiltInParameter.RBS_ELEC_SHORT_CIRCUIT_RATING);
            paramDBFormRating = db.get_Parameter(PowerBIM_Constants.paramGuidDBFormRating);
            paramDBBusFaultLevel = db.get_Parameter(PowerBIM_Constants.paramGuidDBFaultLevel);
            paramDBSurgeProtection = db.get_Parameter(PowerBIM_Constants.paramGuidDSurgeProtection);
            paramDBMetering = db.get_Parameter(PowerBIM_Constants.paramGuidDBMetering);
            paramDBfinalCCTMaxVDPC = db.get_Parameter(PowerBIM_Constants.paramGuidDBFinalCCTmaxVDPC);

            paramDBEFLiR = db.get_Parameter(PowerBIM_Constants.paramGuidDBEFLiR);
            paramDBEFLiX = db.get_Parameter(PowerBIM_Constants.paramGuidDBEFLiX);
            paramDBVD = db.get_Parameter(PowerBIM_Constants.paramGuidDBVD);
            paramDBPSCC = db.get_Parameter(PowerBIM_Constants.paramGuidDBPSCC);
            paramUpstreamRating = db.get_Parameter(PowerBIM_Constants.paramGuidDBupstreamRating);
            paramDBchk = db.get_Parameter(PowerBIM_Constants.paramGuidChkDB);

            paramRevitDiversifiedTotalPhaseCurrentR = db.get_Parameter(PowerBIM_Constants.paramGuidDiversifiedTotalLoadR);
            paramRevitDiversifiedTotalPhaseCurrentW = db.get_Parameter(PowerBIM_Constants.paramGuidDiversifiedTotalLoadW);
            paramRevitDiversifiedTotalPhaseCurrentB = db.get_Parameter(PowerBIM_Constants.paramGuidDiversifiedTotalLoadB);
            paramPBLength_Beca_Circuit_Length_Manual = db.get_Parameter(PowerBIM_Constants.paramGuidPBLength_Circuit_Length_Manual);

            _dbParamPBVDCalcOther = db.get_Parameter(PowerBIM_Constants.paramGuidPBVDCalcOther);
            _dbParamPBVDCalcPower = db.get_Parameter(PowerBIM_Constants.paramGuidPBVDCalcPower);
            _dbParamPBVDCalcLighting = db.get_Parameter(PowerBIM_Constants.paramGuidPBVDCalcLighting);
            _dbParamPBGPOCalc = db.get_Parameter(PowerBIM_Constants.paramGuidPBGPOCalc);
            _dbParamPBClearingTimePower = db.get_Parameter(PowerBIM_Constants.paramGuidPBClearingTimePower);
            _dbParamPBClearingTimeLighting = db.get_Parameter(PowerBIM_Constants.paramGuidPBClearingTimeLighting);

            _dbParamPBLength_ExtraPerCCT_Other = db.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerCCT_Other);
            _dbParamPBLength_ExtraPerElement_Other = db.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerElement_Other);
            _dbParamPBLength_ExtraPerCCT_Lighting = db.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerCCT_Lighting);
            _dbParamPBLength_ExtraPerElement_Lighting = db.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerElement_Lighting);
            _dbParamPBLength_ExtraPerCCT_Power = db.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerCCT_Power);
            _dbParamPBLength_ExtraPerElement_Power = db.get_Parameter(PowerBIM_Constants.paramGuidPBLength_ExtraPerElement_Power);

            Length_ExtraPerElement_Other = _dbParamPBLength_ExtraPerElement_Other.AsDouble();
            Length_ExtraPerCCT_Other = _dbParamPBLength_ExtraPerCCT_Other.AsDouble();
            Length_ExtraPerElement_Lighting = _dbParamPBLength_ExtraPerElement_Lighting.AsDouble();
            Length_ExtraPerCCT_Lighting = _dbParamPBLength_ExtraPerCCT_Lighting.AsDouble();
            Length_ExtraPerElement_Power = _dbParamPBLength_ExtraPerElement_Power.AsDouble();
            Length_ExtraPerCCT_Power = _dbParamPBLength_ExtraPerCCT_Power.AsDouble();

            IsManuallyLocked = db.LookupParameter("Beca_PB_IsLocked")?.AsInteger() == 1;

            //Get DB Parameter Values: 9 parameters
            DB_Element = db;

            // Get Circuit Naming parameter value
            CircuitNaming = db.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_NAMING).AsValueString();

            // Panel Name
            Schedule_DB_Name = db.Name.ToString();

            //TODO make dynamic
            PanelScheduleView = new FilteredElementCollector(db.Document).OfClass(typeof(PanelScheduleView)).Where(ps => (ps as PanelScheduleView).GetPanel() == db.Id).Cast<PanelScheduleView>().FirstOrDefault();
            NumberOfWays = GetPanelSize(PanelScheduleView) / 3;

            // Validate PRoject DB Data 
            Parameters_Missing = ReadDBData();

            // If we got good data from the parameters, perform DB checks
            Check_DB();


            // Get DB XYZ
            LocationPoint DBlocation = DB_Element.Location as LocationPoint;
            DB_Location = DBlocation.Point;

            // Create a new list of PowerBIM_Circuits to house all circuits on this DB
            CCTs = new List<PowerBIM_CircuitData>();

            DB_ElectricalSystem = ElectricalSystemUtility.GetElementElectricalSystem(DB_Element);

            #region length Calc

            LengthClass = new PowerBIM_DBRoute(pi, this);
            LengthClass.CCT_CalculateCircuitLength();

            var paramLen_Total = DB_ElectricalSystem?.get_Parameter(PowerBIM_Constants.paramGuidLen_Total);
            if (paramLen_Total == null)
            {
                //Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Length To First Circuit Element" + "'. ";
                //Parameters_Good = false;
            }
            else if (paramLen_Total.HasValue == false)
            {
                //Schedule_CCTCheck_Summary += "The parameter '" + "Total circuit length" + "' is empty. ";
                //Values_Missing = true;
            }
            else
            {

                LengthClass.Length_Total = RevitUnitConvertor.InternalToMm(paramLen_Total.AsDouble());
            }
            if (DB_ElectricalSystem != null)
            {
                LengthClass.Circuit_Path_Mode = DB_ElectricalSystem.CircuitPathMode.ToString();
            }

            #endregion

            User_Notes = db.LookupParameter("Beca Inst Use")?.AsString() ?? string.Empty;

        }

        internal bool DB_IsManual()
        {
            if (paramPBLength_Beca_Circuit_Length_Manual != null)
            {
                return paramPBLength_Beca_Circuit_Length_Manual.AsInteger() == 1 ? true : false;
            }
            return false;
        }

        public void readCircuitPathMode()
        {
            if (DB_ElectricalSystem != null)
            {
                //circuit path generation mode
                LengthClass.Circuit_Path_Mode = DB_ElectricalSystem.CircuitPathMode.ToString();
                LengthClass.CCT_CalculateCircuitLength();
            }
        }

        public void Initialise_AllCircuits()
        {
            //Get all the circuits for this panel
            ElementId rqParaId = new ElementId(BuiltInParameter.RBS_ELEC_CIRCUIT_PANEL_PARAM);
            ParameterValueProvider rqProvider = new ParameterValueProvider(rqParaId);
            FilterStringRuleEvaluator rqEval = new FilterStringEquals();

            FilterRule rqRule = null; 
#if TargetYear2023 || TargetYear2024 || TargetYear2025 || TargetYear2026
            rqRule = new FilterStringRule(rqProvider, rqEval, Schedule_DB_Name);
#else
            rqRule = new FilterStringRule(rqProvider, rqEval, Schedule_DB_Name, false);
#endif

            ElementFilter rqFilter = new ElementParameterFilter(rqRule);
            FilteredElementCollector ElecSysCol = new FilteredElementCollector(Project_Info.Document).OfClass(typeof(ElectricalSystem)).WherePasses(rqFilter);

            // for each circuit on panel, add circuit class 
            foreach (ElectricalSystem sys in ElecSysCol)
            {
                // Create new circuit instance
                PowerBIM_CircuitData new_cct = new PowerBIM_CircuitData(Project_Info, this, sys);

                //Update data for this new class
                new_cct.Get_CircuitParameters();
                new_cct.Get_CircuitCheckResultFromRevit();
                new_cct.Populate_CableParameters();
                new_cct.Populate_BreakerParameters();
                new_cct.Refresh_DerrivedCircuitProperties();

                //generate warnings
                new_cct.CheckDefinedCableWarning(new_cct.Schedule_Cable_To_First);
                new_cct.CheckDashCableWarning(new_cct);
                new_cct.CheckEmergencyLightingExistsWarning();
                new_cct.CheckDeratingFactorIsAboveOneWarning(new_cct.Schedule_Derating_Factor);
                new_cct.CheckRCDElementIsPresentWarning(new_cct.CCT_RCD_ElementIsPresent);
                // Add warning messages (non critical)
                if (new_cct.Warning_Count > 0)
                {
                    new_cct.AddWarningMessages();
                }

                //if (new_cct.BadCircuits != null)
                //{
                //    foreach (var item in new_cct.BadCircuits)
                //    {
                //        BadCircuitNumbers.Add(item);
                //    }
                //}

                // Add this to our CCTs collection
                CCTs.Add(new_cct); // TODO add some error handling here
            }

            LockedCircuits = CCTs.Where(x => x.IsLocked).ToList();

            // Run powerBIM

            // Determine diversified DB loads based on circuit diversity ratings
            CalculateDiversifiedCircuitLoad();

            // Add up howe many pass /fail ccts
            Check_PassFailWarningCount();
        }


        private bool ReadDBData()
        {
            // Reset DB Check Result
            DB_Check_Result = "OK";
            DB_Check_Warnings = "None";

            //
            // NON CRITICAL PARAMETERS
            //

            // Feeder Cable Size 
            if (paramDBFeederCable == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Feeder Cable" + "'. ";
            else if (string.IsNullOrEmpty(paramDBFeederCable.AsString()))
            {
                DB_Check_Warnings += "The parameter '" + "Feeder Cable" + "' is empty. ";
                Schedule_Feeder_Cable = "";
            }
            else
                Schedule_Feeder_Cable = paramDBFeederCable.AsString();

            // DB Location
            if (paramDBLocation == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "DB Location" + "'. ";
            else if (string.IsNullOrEmpty(paramDBLocation.AsString()))
            {
                DB_Check_Warnings += "The parameter '" + "DB Location" + "' is empty. ";
                Schedule_Location = "";
            }
            else
                Schedule_Location = paramDBLocation.AsString();

            // Seismic Category
            if (paramDBSeismicCategory == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Beca Siesmic Category" + "'. ";
            else if (string.IsNullOrEmpty(paramDBSeismicCategory.AsString()))
            {
                DB_Check_Warnings += "The parameter '" + "Beca Siesmic Category" + "' is empty. ";
                Schedule_Seismic_Category = "";
            }
            else
                Schedule_Seismic_Category = paramDBSeismicCategory.AsString();

            // DB Device fault rating
            if (paramDeviceFaultRating == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Device Fault Rating" + "'. ";
            else if (string.IsNullOrEmpty(paramDeviceFaultRating.AsString()))
            {
                DB_Check_Warnings += "The parameter '" + "Device Fault Rating" + "' is empty. ";
                Schedule_Device_Fault_Rating = "";
            }
            else
                Schedule_Device_Fault_Rating = paramDeviceFaultRating.AsString();

            // Form Rating
            if (paramDBFormRating == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Beca Form Rating" + "'. ";
            else if (string.IsNullOrEmpty(paramDBFormRating.AsString()))
            {
                DB_Check_Warnings += "The parameter '" + "Beca Form Rating" + "' is empty. ";
                Schedule_Form_Rating = "";
            }
            else
                Schedule_Form_Rating = paramDBFormRating.AsString();

            // Bus Fault Level
            if (paramDBBusFaultLevel == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Beca Fault Level" + "'. ";
            else if (string.IsNullOrEmpty(paramDBBusFaultLevel.AsString()))
            {
                DB_Check_Warnings += "The parameter '" + "Beca Fault Level" + "' is empty. ";
                Schedule_Bus_Fault_Level = "";
            }
            else
                Schedule_Bus_Fault_Level = paramDBBusFaultLevel.AsString();

            // Surge Protection
            if (paramDBSurgeProtection == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Beca Surge Protection" + "'. ";
            else if (string.IsNullOrEmpty(paramDBSurgeProtection.AsString()))
            {
                DB_Check_Warnings += "The parameter '" + "Beca Surge Protection" + "' is empty. ";
                Schedule_Surge_Protection = "";
            }
            else
                Schedule_Surge_Protection = paramDBSurgeProtection.AsString();

            // Check Metering
            if (paramDBMetering == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Beca Check Metering" + "'. ";
            else if (string.IsNullOrEmpty(paramDBMetering.AsString()))
            {
                DB_Check_Warnings += "The parameter '" + "Beca Check Metering" + "' is empty. ";
                Schedule_Metering = "";
            }
            else
                Schedule_Metering = paramDBMetering.AsString();


            //
            // CRITICAL PARAMETERS
            //

            // Main Switch Rating
            if (paramMansSWrating == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Main Switch Rating" + "'. ";
            else if (paramMansSWrating.AsDouble() == 0)
                DB_Check_Result += "The parameter '" + "Main Switch Rating" + "' is empty. ";
            else
                Schedule_Main_Switch_Rating = paramMansSWrating.AsDouble();

            // DB Final circuit max VD percentage
            if (paramDBfinalCCTMaxVDPC == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Final CCT Max VD" + "'. ";
            else if (paramDBfinalCCTMaxVDPC.AsDouble() == 0)
            {
                DB_Check_Warnings += "The parameter '" + "Final CCT Max VD" + "' is empty. ";
                Schedule_Final_Circuit_MaxVD = 0;
            }
            else
                Schedule_Final_Circuit_MaxVD = paramDBfinalCCTMaxVDPC.AsDouble();

            // Upstream Device Rating
            if (paramUpstreamRating == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Upstream Device Rating" + "'. ";
            else if (paramUpstreamRating.AsDouble() == 0)
            {
                DB_Check_Warnings += "The parameter '" + "Upstream Device Rating" + "' is empty. ";
                Upstream_Device_Rating = 0;
            }
            else
                Upstream_Device_Rating = paramUpstreamRating.AsDouble();

            // Earth Fault Loop Impedance (R)
            if (paramDBEFLiR == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "DB EFLi R" + "'. ";
            else if (paramDBEFLiR.AsDouble() == 0)
            {
                DB_Check_Warnings += "The parameter '" + "DB EFLi R" + "' is empty. ";
                EFLI_R = 0;
            }
            else
                EFLI_R = paramDBEFLiR.AsDouble();

            // Earth Fault Loop Impedance (X)
            if (paramDBEFLiX == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "DB EFLi X" + "'. ";
            else if (paramDBEFLiX.AsDouble() == 0)
            {
                DB_Check_Warnings += "The parameter '" + "DB EFLi X" + "' is empty. ";
                EFLI_X = 0;
            }
            else
                EFLI_X = paramDBEFLiX.AsDouble();

            // DB volt Drop (X)
            if (paramDBVD == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "DB VD" + "'. ";
            else if (paramDBVD.AsDouble() == 0)
            {
                DB_Check_Warnings += "The parameter '" + "DB VD" + "' is empty. ";
                DBVD = 0;
            }
            else
                DBVD = paramDBVD.AsDouble();

            // DB Prospective Short Circuit Curren
            if (paramDBPSCC == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "DB PSCC" + "'. ";
            else if (paramDBPSCC.AsDouble() == 0)
            {
                DB_Check_Warnings += "The parameter '" + "DB PSCC" + "' is empty. ";
                PSCC = 0;
            }
            else
                PSCC = paramDBPSCC.AsDouble();

            // Check DiversifiedTotalPhaseCurrent parameters
            if (paramRevitDiversifiedTotalPhaseCurrentR == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Revit Diversified Total Phase Current R" + "'. ";
            else if (paramRevitDiversifiedTotalPhaseCurrentR.AsDouble() == 0)
            {
                DB_Check_Warnings += "The parameter '" + "Revit Diversified Total Phase Current R" + "' is empty. ";
                RevitDiversifiedTotalPhaseCurrentR = 0;
            }
            else
                RevitDiversifiedTotalPhaseCurrentR = paramRevitDiversifiedTotalPhaseCurrentR.AsDouble();

            if (paramRevitDiversifiedTotalPhaseCurrentB == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Revit Diversified Total Phase Current R" + "'. ";
            else if (paramRevitDiversifiedTotalPhaseCurrentB.AsDouble() == 0)
            {
                DB_Check_Warnings += "The parameter '" + "Revit Diversified Total Phase Current R" + "' is empty. ";
                RevitDiversifiedTotalPhaseCurrentB = 0;
            }
            else
                RevitDiversifiedTotalPhaseCurrentB = paramRevitDiversifiedTotalPhaseCurrentB.AsDouble();

            if (paramRevitDiversifiedTotalPhaseCurrentW == null)
                DB_Check_Result += "Revit project is missing the parameter '" + "Revit Diversified Total Phase Current W" + "'. ";
            else if (paramRevitDiversifiedTotalPhaseCurrentW.AsDouble() == 0)
            {
                DB_Check_Warnings += "The parameter '" + "Revit Diversified Total Phase Current W" + "' is empty. ";
                RevitDiversifiedTotalPhaseCurrentW = 0;
            }
            else
                RevitDiversifiedTotalPhaseCurrentW = paramRevitDiversifiedTotalPhaseCurrentW.AsDouble();



            if (DB_Check_Result == "OK")
                // Paramters check fail
                return false;
            else
                // Paramters check fail
                return true;

        }

        internal bool OpenPathCustomisingView()
        {
            return LengthClass.OpenPathCustomisingView();
        }

        public bool CalculateDiversifiedCircuitLoad()
        {
            // 
            // Go through each circuits (once circuits are calculated) and sum up each diversified circuit load 
            //
            Diversified_Total_Phase_Current_R = 0;
            Diversified_Total_Phase_Current_W = 0;
            Diversified_Total_Phase_Current_B = 0;

            UnDiversified_Total_Phase_Current_R = 0;
            UnDiversified_Total_Phase_Current_W = 0;
            UnDiversified_Total_Phase_Current_B = 0;

            foreach (PowerBIM_CircuitData cct in CCTs)
            {
                cct.CalculateDiversifiedCircuitLoad();

                Diversified_Total_Phase_Current_R = Math.Round(Diversified_Total_Phase_Current_R + cct.CCT_Diversified_Current_Phase_A, 2);
                Diversified_Total_Phase_Current_W = Math.Round(Diversified_Total_Phase_Current_W + cct.CCT_Diversified_Current_Phase_B, 2);
                Diversified_Total_Phase_Current_B = Math.Round(Diversified_Total_Phase_Current_B + cct.CCT_Diversified_Current_Phase_C, 2);

                UnDiversified_Total_Phase_Current_R = Math.Round(UnDiversified_Total_Phase_Current_R + cct.CCT_Undiversified_Current_Phase_A, 2);
                UnDiversified_Total_Phase_Current_W = Math.Round(UnDiversified_Total_Phase_Current_W + cct.CCT_Undiversified_Current_Phase_B, 2);
                UnDiversified_Total_Phase_Current_B = Math.Round(UnDiversified_Total_Phase_Current_B + cct.CCT_Undiversified_Current_Phase_C, 2);
            }

            return true; //todo
        }

        private bool Check_DBData()
        {
            //
            // [2.01] Check if DB parameters are valid
            //
            DB_Check_Result = "";

            // TODO make this param a double so this parsing isnt required!!
            if (Double.TryParse(Schedule_Device_Fault_Rating, out double dblDeviceFaultRating) == false)
            {
                DB_Check_Result += "(Error in Device Fault Rating)";
                return false;
            }
            else
                Device_kA_Rating = dblDeviceFaultRating;

            //* DB Data has zero values ?
            if (Schedule_Main_Switch_Rating == 0 || Upstream_Device_Rating == 0 || EFLI_R == 0 || EFLI_X == 0 || DBVD == 0 || PSCC == 0 || Schedule_Final_Circuit_MaxVD == 0 || Device_kA_Rating == 0)
            {
                DB_Check_Result += "(Some DB calculation parameters are zero)";
                return false;
            }

            //** If no zero value, then Check Device Fault Rating
            else
            {
                if (Device_kA_Rating >= PSCC)
                {
                    DB_Check_Result += "PASS, DBPSCC / Device Fault Rating : " + PSCC.ToString() + " / " + Device_kA_Rating.ToString();
                    return true;
                }
                else
                {
                    DB_Check_Result += "(Device Fault Rating < PSCC)";
                    return false;
                }
            }

        }

        public void Check_DB()
        {
            // Check DB results
            if (!Parameters_Missing)
                Data_Good = Check_DBData();

            Update_GUI_Notes();
        }


        public void Run_DBCheckAllCircuits(out string dBNotCommited)
        {
            dBNotCommited = string.Empty;

            //Initial values for DB Check Results
            string strDBcheck = "";

            int Count_CableValid = 0;
            int Count_CableInvalid = 0;
            int Count_CableWithWarnings = 0;

            // Check DB has all the parameters we need. If missing, exit application
            if (Parameters_Missing == true)
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("PowerBIM", "PowerBIM Error: Your project is missing vital DB parameters"
                    + Environment.NewLine + Schedule_DB_Name + ": " + DB_Check_Result);
            }

            // Commit DB check result
            try
            {
                paramDBchk.Set(DB_Check_Result);
            }
            catch (Exception)
            {
                dBNotCommited = DB_Element.Name;
            }

            //
            // -------------------------------------------- Start Checking All the Circuits in this DB ------------------------------------------------------
            //

            int intCCTcount = 0;

            // Go through each circuit
            foreach (PowerBIM_CircuitData CCT in CCTs)
            {
                // First, update all information inclase something has changedsha
                CCT.Refresh_DerrivedCircuitProperties();


                // [2.02] Check if the circuit is not a "Spare" or "Space"
                if (!CCT.CCT_Is_Spare_Or_Space)
                {
                    // If we got good circuit breaker data, and cable data we good to go - booyah!
                    if (CCT.Breaker.Data_Good == true && CCT.Cable_To_First.Data_Good == true && CCT.Cable_To_Final.Data_Good == true)
                    {
                        //
                        //      ** This is where the smart stuff hapens.**
                        //
                        //      The cable check has the following phases
                        //      1) Get/populate all inputs into the check process
                        //      2) Calculate important calc results using these values
                        //      3) Perform Circuit Checks 
                        //      4) Write these results back to the revit schedule
                        //

                        // Perform all circuit calculation operations on circuit
                        CCT.PowerBIM_CalculationEngine();

                        // Perform all circuit checks on new calculation data
                        //CCT.Check_Pass = PowerBIM_Calculations.PowerBIMCircuitCheckEngine(CCT);
                    }
                    else
                    {
                        // TODO: Data is not good, we need to do something
                    }
                    CCT.RunPowerBIMCheck();
                    //
                    // Writing back the ciruit check results to DB schedule
                    // 

                    // Update cable names and install method text strings.
                    CCT.Schedule_Cable_To_First = CCT.Cable_To_First.Cable_Name;
                    CCT.Schedule_Cable_To_Final = CCT.Cable_To_Final.Cable_Name;
                    CCT.SetSchedule_CableName();

                    // Set install method
                    CCT.SetSchedule_InstallMethod();


                    // Write back pass message
                    if (CCT.Check_Pass == true)
                    {
                        //Mark message as OK
                        CCT.AddPassMessage();
                        Count_CableValid++;
                    }
                    else
                    {
                        // Add error messages
                        CCT.AddErrorMessages();
                        Count_CableInvalid++;
                    }

                    // Add warning messages (non critical)
                    if (CCT.Warning_Count > 0)
                    {
                        CCT.AddWarningMessages();
                        Count_CableWithWarnings++;
                    }

                    // Write back the 10 CCT CHECK RESULT fields to revit
                    //if (Project_Info.GUI_CheckDB_Run)
                    //    CCT.SetSchedule_CheckParamteters();

                    // Jump to next circuit
                    intCCTcount++;
                }

            } // ------------------------------------ END OF CIRCUIT FOR EACH LOOP ------------------------------------------------

            //
            StringBuilder sb;
            Commit_CircuitData(out sb);
            if (sb.Length > 0)
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Info", "These circuits cannot be commited:\n\n" + sb.ToString());

            // Add Diveristy load calc
            if (!Commit_DiversifiedPhaseCurrent())
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Failed", "'Diversified DB Currents' could not be committed");

            // Append DB summary
            DB_Result_Summary = Schedule_DB_Name + " PowerBIM Results: " + strDBcheck + "\n"
                                                    + " - Circuits Passed: " + Count_CableValid
                                                    + ",  Failed: " + Count_CableInvalid
                                                    + ",  Warnings: " + Count_CableWithWarnings + "\n";

        }



        public void Check_PassFailWarningCount()
        {
            // Reset counters
            Result_PassCount = 0;
            Result_WarningCount = 0;
            Result_FailCount = 0;

            foreach (PowerBIM_CircuitData CCT in CCTs)
            {
                if (!CCT.CCT_Is_Spare_Or_Space)
                {
                    if (CCT.Check_Pass)
                    {
                        Result_PassCount++;
                    }
                    else
                    {
                        Result_FailCount++;
                    }

                    if (CCT.Warning_Count > 0)
                    {
                        Result_WarningCount++;
                    }
                }
            }

            // If we have no failing circuits then set all-pass flag
            DB_All_Circuits_Pass = Result_FailCount == 0;

        }

        public void Commit_ManualValue(bool value)
        {
            if (paramPBLength_Beca_Circuit_Length_Manual != null)
            {
                using (Transaction tx = new Transaction(DB_Element.Document, "Update PowerBim Param"))
                {
                    tx.Start();
                    paramPBLength_Beca_Circuit_Length_Manual.Set(value ? 1 : 0);
                    tx.Commit();

                }
            }

        }


        public bool Commit_DiversifiedPhaseCurrent()
        {
            //
            // Write Diversified phase loads back to revit
            //
            try
            {
                paramRevitDiversifiedTotalPhaseCurrentR.Set(Diversified_Total_Phase_Current_R);
                paramRevitDiversifiedTotalPhaseCurrentW.Set(Diversified_Total_Phase_Current_W);
                paramRevitDiversifiedTotalPhaseCurrentB.Set(Diversified_Total_Phase_Current_B);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }


        public void Commit_DBHeaderData()
        {
            //
            // Commit new DB calcualtion paramters back to revit
            //

            using (Transaction Trans_DB = new Transaction(Project_Info.Document))
            {
                if (Trans_DB.Start(BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()) == TransactionStatus.Started)
                {
                    // Commit DB parameters back to revit 
                    paramDBFeederCable.Set(Schedule_Feeder_Cable);
                    paramMansSWrating.Set(Schedule_Main_Switch_Rating);
                    paramDBLocation.Set(Schedule_Location);
                    paramDBSeismicCategory.Set(Schedule_Seismic_Category);
                    paramDeviceFaultRating.Set(Schedule_Device_Fault_Rating);
                    paramDBFormRating.Set(Schedule_Form_Rating);
                    paramDBBusFaultLevel.Set(Schedule_Bus_Fault_Level);
                    paramDBSurgeProtection.Set(Schedule_Surge_Protection);
                    paramDBMetering.Set(Schedule_Metering);
                    paramDBfinalCCTMaxVDPC.Set(Schedule_Final_Circuit_MaxVD);

                    paramUpstreamRating.Set(Upstream_Device_Rating);
                    paramDBEFLiR.Set(EFLI_R);
                    paramDBEFLiX.Set(EFLI_X);
                    paramDBVD.Set(DBVD);
                    paramDBPSCC.Set(PSCC);

                    if (TransactionStatus.Committed != Trans_DB.Commit())
                    {
                        UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Failed", "Transaction could not be committed");
                    }
                }

                //
                // now verify the data
                //

                // Validate New DB Data
                Parameters_Missing = ReadDBData();

                Check_DB();

                return;
            }
        }

        public bool SetSchedule_DiversifiedTotal()
        {
            try
            {

                paramRevitDiversifiedTotalPhaseCurrentR.Set(Diversified_Total_Phase_Current_R);
                paramRevitDiversifiedTotalPhaseCurrentW.Set(Diversified_Total_Phase_Current_W);
                paramRevitDiversifiedTotalPhaseCurrentB.Set(Diversified_Total_Phase_Current_B);
                return true;
            }
            catch (Exception)
            {

                return false;

            }

        }


        

        public void Commit_CircuitData(out StringBuilder circuitNotCommited)
        {
            circuitNotCommited = new StringBuilder();
            //
            // Commit new DB calcualtion paramters back to revit
            //
            foreach (PowerBIM_CircuitData cct in CCTs)
            {
                try
                {
                    // Commit CCT parameters back to revit 
                    // TODO add if staements for each change 
                    cct.SetSchedule_CableLengths();
                    cct.SetSchedule_CableName();
                    cct.SetSchedule_CheckParamteters();
                    cct.SetSchedule_RCDDeratingDiversityAndRevision();
                    cct.SetSchedule_InstallMethod();
                    cct.SetSchedule_BreakerData();
                    cct.SetSchedule_Description();
                    cct.SetCurrent();
                }
                catch (Exception ex)
                {
                    circuitNotCommited.AppendLine(cct.CCT_Number);
                }

            }
            CalculateDiversifiedCircuitLoad();

            SetSchedule_DiversifiedTotal();

            /*
            // promt the user to confirm they want to commit to revit
            TaskDialog td = new TaskDialog("Revit");
            td.MainContent = "Click either [OK] to Commit, or [Cancel] to Roll Back the transaction";
            TaskDialogCommonButtons buttons = TaskDialogCommonButtons.Ok | TaskDialogCommonButtons.Cancel;
            td.CommonButtons = buttons;

            //td.Show();

            if (TaskDialogResult.Ok == td.Show())
            {
                if (TransactionStatus.Committed != Trans_CCT.Commit())
                {
                    TaskDialog.Show("Failed", "Circuit Data could not be committed");
                    return false;
                }
            }
            else
            {
                Trans_CCT.RollBack();
            }
            */
        }

        public void Update_GUI_Notes()
        {
            if ((DB_Check_Result.ToUpper().Contains("ERROR")))
            {
                GUI_Notes = "View Errors";
            }

            if (!(DB_Check_Warnings == "None"))
            {
                GUI_Notes = "View Warnings";
            }

            GUI_Notes_Message = "DB Check Errors:\n" + DB_Check_Result + "\n\n" + "DB Check Warnings:\n" + DB_Check_Warnings;
        }

        private int GetPanelSize(PanelScheduleView panelScheduleView)
        {
            if (panelScheduleView == null)
                return 0;

            int size = 0;
            for (int i = 0; i < 200; i++)
            {
                if (!panelScheduleView.IsRowInCircuitTable(i))
                {
                    size = i - 2;
                    break;
                }
            }

            if (size < 0)
                return 0;
            else
                return size;
        }

        public void Commit_DBSettings()
        {
            if (Parameters_Changed)
            {
                SetAdvancedSettingsParameter();
            }
        }

        public void SetAdvancedSettingsParameter()
        {
            // Open a new transaction
            Active_Trans.Start(BecaTransactionsNames.PowerBIM_UpdateAdvancedSettings.GetHumanReadableString());

            // Set relevent parameters
            _dbParamPBVDCalcOther.Set((int)OtherVDCalculation);
            _dbParamPBVDCalcPower.Set((int)PowerVDCalculation);
            _dbParamPBVDCalcLighting.Set((int)LightingVDCalculation);
            _dbParamPBGPOCalc.Set(GPO_Calc_Integer);
            _dbParamPBClearingTimePower.Set(Clearing_Time_Power);
            _dbParamPBClearingTimeLighting.Set(Clearing_Time_Lighting);

            _dbParamPBLength_ExtraPerElement_Lighting.Set(Length_ExtraPerElement_Lighting);
            _dbParamPBLength_ExtraPerCCT_Lighting.Set(Length_ExtraPerCCT_Lighting);
            _dbParamPBLength_ExtraPerElement_Power.Set(Length_ExtraPerElement_Power);
            _dbParamPBLength_ExtraPerCCT_Power.Set(Length_ExtraPerCCT_Power);
            _dbParamPBLength_ExtraPerElement_Other.Set(Length_ExtraPerElement_Other);
            _dbParamPBLength_ExtraPerCCT_Other.Set(Length_ExtraPerCCT_Other);

            // Close the transaction
            Active_Trans.Commit();
        }
    }
}
