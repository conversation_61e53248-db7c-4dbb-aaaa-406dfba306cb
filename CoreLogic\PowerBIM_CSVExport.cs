﻿using Autodesk.Revit.UI;
using Common.Utilities;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MEP.PowerBIM_5.UI.Forms;
using Common.UI.Forms;


namespace MEP.PowerBIM_5.CoreLogic
{
    //
    // This module handles the two file export functions from PowerBIM. 
    //
    //  1) Verification Report 
    //      - creates a detailed export of all calculation results and workings. 
    //      - this can be used for verification (may be easier on large jobs) or can be used to keep record of the calculations made. 
    //  2) Summary Report 
    //      - creates a reduced exports showing the same information that would be issued on a DB schedule to the contractor. 
    //      - usefull if you want to use the excel/word template and transpose the data
    //

    class PowerBIM_CSVExport
    {
        public PowerBIM_ProjectInfo projInfo { get; set; }

        //
        // Constructors
        //
        public PowerBIM_CSVExport(PowerBIM_ProjectInfo pi)
        {
            projInfo = pi;
        }

        /// <summary>
        /// Recalculates all circuit data to ensure fresh EFLI and voltage drop values before export
        /// This matches the same process that happens in the Enhanced Circuit Edit form
        /// </summary>
        private void RecalculateAllCircuitData(List<PowerBIM_DBData> DBs)
        {
            foreach (var DB in DBs)
            {
                foreach (var circuit in DB.CCTs)
                {
                    // Refresh derived circuit properties (same as Enhanced Circuit Edit form)
                    circuit.Refresh_DerrivedCircuitProperties();

                    // Run PowerBIM check to recalculate EFLI and voltage drop values
                    circuit.RunPowerBIMCheck();

                }
            }

        }

        internal void Export_CSV(List<PowerBIM_DBData> DBs, bool ShowVerification)
        {
            if (ShowVerification)
            {
                RecalculateAllCircuitData(DBs);
            }

            //
            // Export PowerBIM DB Schedules
            //

            // Generating a progress bar for the export so the user can see progress.
            //
            PB_ProgressBar pb = new PB_ProgressBar();
            pb.Reset();
            pb.SetScope(8, 1);
            pb.SetTask("Exporting", 1);
            pb.SetCaption("Export - Initialising ...", 1);
            pb.LevelUp();

            // Configuring the progress bar.
            pb.Reset(pb.Level + 1);
            pb.SetCaption("Initialising ...", pb.Level + 1);
            pb.LevelUp();

            // Setting the scope of the progress bar.
            pb.SetScope(9, pb.Level);

            // Generating new date tme stamps
            pb.Step("Generating new date:time stamps ...");
            projInfo.GetDateTimeInfo();

            // Write Report Document Headings         
            if (ShowVerification)
                VerifcationReport_WriteDocumentStart(projInfo.File_Base_Info);
            else
                SummaryReport_WriteDocumentStart(projInfo.File_Base_Info);

            //StreamWriter outputFile = new StreamWriter("testoutput.csv", false, new UTF8Encoding(true));

            // DB counter
            int dbCount = 0;
            string Summary = "";


            foreach (PowerBIM_DBData DB in DBs)
            {
                string currentDBName = DB.Schedule_DB_Name;

                //
                // Write Report DB Headings if enabled
                //

                // ----- Write Verification Report DB Heading -----
                if (ShowVerification)
                    VerificationReport_WriteHeading(DB);
                else
                    SummaryReport_WriteHeading(DB);


                foreach (PowerBIM_CircuitData CCT in DB.CCTs)
                {
                    // and copy another row to the summary sheet
                    pb.Step(string.Format("Adding another CCT to the summary sheet"));

                    //Write cable data to VERIFICATION SUMMARY REPORT
                    if (ShowVerification)
                        VerificationReport_WriteCircuitEntry(CCT);
                    else
                        SummaryReport_WriteCircuitEntry(CCT);
                }

                // and copy another row to the summary sheet
                pb.Step(string.Format("Adding DB Entry #" + dbCount + " to the summary sheet"));

                // increment DB counter. 
                dbCount++;
                Summary += DB.DB_Result_Summary;
            }

            // Closing the pb.
            pb.LevelDown();

            // Closing the sheet.
            pb.Step("Saving the file ...");


            // ----- Write Report Summary -----
            if (ShowVerification)
                VerificationReport_WriteEnd(Summary);
            else
                SummaryReport_WriteEnd(Summary);

            // Completed.
            pb.Step("Completed");
            System.Threading.Thread.Sleep(1000);

            // Closing the pb.
            pb.LevelDown();

            return;
        }

        internal void SummaryReport_WriteDocumentStart(string StrFileBaseInfo)
        {
            //
            // Write summary report document start header
            //
            using (StreamWriter SW = File.AppendText(projInfo.ReportFile_CableSummary_CSV))
            {
                SW.WriteLine(StrFileBaseInfo);
                SW.WriteLine();
                SW.WriteLine();
            }
        }

        internal void VerifcationReport_WriteDocumentStart(string StrFileBaseInfo)
        {
            //
            // Write Verification report document start header
            //
            using (StreamWriter SW = File.AppendText(projInfo.ReportFile_VerificationSummary_CSV))
            {
                SW.WriteLine(StrFileBaseInfo);
                SW.WriteLine();
                SW.WriteLine();
            }
        }

        internal void SummaryReport_WriteHeading(PowerBIM_DBData dbData)
        {
            //
            // Write Summary Report DB Heading
            //
            using (StreamWriter SW = File.AppendText(projInfo.ReportFile_CableSummary_CSV))
            {
                SW.WriteLine();
                SW.WriteLine("Panel Name: " + dbData.Schedule_DB_Name);
                SW.WriteLine();
                SW.WriteLine("Main Switch Rating (A): " + "," + dbData.Schedule_Main_Switch_Rating.ToString());
                SW.WriteLine("Feeder Cable Size: " + "," + dbData.Schedule_Feeder_Cable);
                SW.WriteLine("Location of DB: " + "," + dbData.Schedule_Location);
                SW.WriteLine("Seismic Category: " + "," + dbData.Schedule_Seismic_Category);
                SW.WriteLine("Device Fault Rating (kA): " + "," + dbData.Schedule_Device_Fault_Rating);
                SW.WriteLine("Form Rating: " + "," + dbData.Schedule_Form_Rating);
                SW.WriteLine("Bus Fault Level " + "," + dbData.Schedule_Bus_Fault_Level);
                SW.WriteLine("Surge Protection: " + "," + dbData.Schedule_Surge_Protection);
                SW.WriteLine("Final Circuit Max Volt Drop: " + "," + dbData.Schedule_Final_Circuit_MaxVD.ToString());
                SW.WriteLine();
                SW.WriteLine(PowerBIM_Constants.strCableSummaryReportHead);
            }
        }

        internal void VerificationReport_WriteHeading(PowerBIM_DBData dbData)
        {
            //
            // Write Verification Report DB Heading 
            //
            using (StreamWriter SW = File.AppendText(projInfo.ReportFile_VerificationSummary_CSV))
            {
                SW.WriteLine();
                SW.WriteLine("Panel Name: " + dbData.Schedule_DB_Name);
                SW.WriteLine();
                SW.WriteLine("Main Switch Rating: " + "," + dbData.Schedule_Main_Switch_Rating.ToString());
                SW.WriteLine("Feeder Cable Size: " + "," + dbData.Schedule_Feeder_Cable);
                SW.WriteLine("Protective Device Rating: " + "," + dbData.Schedule_Device_Fault_Rating);
                SW.WriteLine("Final Circuit Max Volt Drop: " + "," + dbData.Schedule_Final_Circuit_MaxVD.ToString());
                SW.WriteLine("DB EFLI (R): " + "," + dbData.EFLI_R);
                SW.WriteLine("DB EFLI (X): " + "," + dbData.EFLI_X);
                SW.WriteLine("DB VD: " + "," + dbData.DBVD);
                SW.WriteLine("DB PSCC: " + "," + dbData.PSCC);
                SW.WriteLine();
                SW.WriteLine(PowerBIM_Constants.strVerificationReportHead);
            }
        }


        internal void SummaryReport_WriteCircuitEntry(PowerBIM_CircuitData cct)
        {
            //
            // Write circuit entry to CABLE SUMMARY REPORT
            //
            string entry = "";

            using (StreamWriter SW = File.AppendText(projInfo.ReportFile_CableSummary_CSV))
            {
                if (cct.CCT_Number != null)
                    entry += cct.CCT_Number.Replace(",", ";");

                entry += ",";

                entry += cct.Breaker.Schedule_Trip_Rating.ToString() + ","
                    + cct.Breaker.Schedule_Curve_Type + ","
                    + cct.Breaker.Schedule_Protective_Device + ",";

                if (cct.Schedule_RCD != null)
                    entry += cct.Schedule_RCD.Replace(",", ";");

                entry += ",";

                if (cct.Schedule_Other_Controls != null)
                    entry += cct.Schedule_Other_Controls.Replace(",", ";");

                entry += ",";

                entry += cct.Cable_To_First.Cable_Name + ","
                    + cct.Cable_To_Final.Cable_Name + ","
                    + cct.LengthClass.Length_Total.ToString() + ",";

                if (cct.Schedule_Description != null)
                    entry += cct.Schedule_Description.Replace(",", ";");

                entry += ",";

                entry += cct.Schedule_Revision;

                SW.WriteLine(entry);
            }
        }


        internal void VerificationReport_WriteCircuitEntry(PowerBIM_CircuitData cct)
        {
            //
            // Write cable data to VERIFICATION SUMMARY REPORT
            //
            string entry = "";

            using (StreamWriter SW = File.AppendText(projInfo.ReportFile_VerificationSummary_CSV))
            {
                if (cct.CCT_Number != null)
                    entry += cct.CCT_Number.Replace(",", ";");

                entry += ",";

                entry += cct.Breaker.Schedule_Trip_Rating.ToString() + ","
                    + cct.Breaker.Schedule_Curve_Type + ","
                    + cct.Breaker.Schedule_Protective_Device + ",";

                if (cct.Schedule_RCD != null)
                    entry += cct.Schedule_RCD.Replace(",", ";");

                entry += ",";

                if (cct.Schedule_Other_Controls != null)
                    entry += cct.Schedule_Other_Controls.Replace(",", ";");

                entry += ",";

                entry += cct.Cable_To_First.Cable_Name + ","
                    + cct.Cable_To_Final.Cable_Name + ","
                    + cct.LengthClass.Length_To_First.ToString() + ","
                    + cct.LengthClass.Length_Total.ToString() + ","
                    + cct.Schedule_Install_Method + ","
                    + cct.Schedule_Derating_Factor + ","
                    + cct.Number_Of_Poles.ToString() + ",";

                if (cct.Schedule_Description != null)
                    entry += cct.Schedule_Description.Replace(",", ";");

                entry += ", ,"

                    + cct.Cable_To_First.R_Operating_Active + ","
                    + cct.Cable_To_Final.R_Operating_Active + ","
                    + cct.Cable_To_First.R_Operating_Earth + ","
                    + cct.Cable_To_Final.R_Operating_Earth + ","
                    + cct.Cable_To_First.X_Max_Active + ","
                    + cct.Cable_To_Final.X_Max_Active + ","
                    + cct.Cable_To_First.X_Max_Earth + ","
                    + cct.Cable_To_Final.X_Max_Earth + ","
                    + cct.Cable_To_First.Z_Operating_Active + ","
                    + cct.Cable_To_Final.Z_Operating_Active + ","
                    + cct.Cable_To_First.Z_Operating_Earth + ","
                    + cct.Cable_To_Final.Z_Operating_Earth

                    + ", ,"

                    + cct.Cable_To_First.I_Rated + ","
                    + cct.Cable_To_Final.I_Rated + ","
                    + cct.GetCurrent() + ","
                    + cct.Cable_To_First.Temperature + ","
                    + cct.Cable_To_Final.Temperature

                    + ", ,"

                    + cct.Breaker.EFLI_Max.ToString() + ","
                    + cct.CalcRes_Total_EFLi.ToString()

                    + ", ,"

                    + cct.CalcRes_Final_Circuit_VD.ToString() + ","
                    + cct.CalcRes_Final_Circuit_VD_Percentage.ToString()

                    + ", ,"

                    + cct.Schedule_CCTCheck_OK + ","
                    + cct.Schedule_CCTCheck_Summary;

                SW.WriteLine(entry);
            }
        }

        internal void SummaryReport_WriteEnd(string strDBsummary)
        {
            //
            // Write Verification Report DB Heading
            //
            using (StreamWriter SW = File.AppendText(projInfo.ReportFile_CableSummary_CSV))
            {
                SW.WriteLine();
                SW.WriteLine();
                SW.WriteLine("**************************** Summary *******************************");
                SW.WriteLine(strDBsummary);
            }
        }

        internal void VerificationReport_WriteEnd(string strDBsummary)
        {
            //
            // Write Verification Report DB Heading 
            //
            using (StreamWriter SW = File.AppendText(projInfo.ReportFile_VerificationSummary_CSV))
            {
                SW.WriteLine();
                SW.WriteLine();
                SW.WriteLine("**************************** Summary *******************************");
                SW.WriteLine(strDBsummary);
            }
        }
    }
}
